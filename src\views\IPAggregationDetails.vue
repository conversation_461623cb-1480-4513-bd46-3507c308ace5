<template>
  <div id="IPDetails">
    <!--    面包屑-->
    <!-- <a-breadcrumb>
      <a-breadcrumb-item
        ><a @click="$router.push({ path: '/' })">{{
          $t("manu.home")
        }}</a></a-breadcrumb-item
      >
      <a-breadcrumb-item
        ><a
          @click="
            $router.push({
              path: '/search',
              query: { query: searchValue, searchWay: searchWay },
            })
          "
          >{{ $t("manu.query_list") }}</a
        >
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ $t("manu.asset_details") }}</a-breadcrumb-item>
    </a-breadcrumb> -->
    <!--    搜索结果-->
    <main class="resultList">
      <!--      左边结果页-->
      <LeftDetails :ip="ip" :port="port" :transport="transport" />
      <!--      右边结果页-->
      <RightDetails
        :ip="ip"
        :port="port"
        :transport="transport"
        :domain="domain"
      />
    </main>
  </div>
</template>

<script>
import { ip_detail_status } from "@/util/memoryUtils";
import LeftDetails from "@/components/LeftDetails";
import RightDetails from "@/components/RightDetails";

export default {
  name: "IPAggregationDetails",
  components: {
    LeftDetails,
    RightDetails,
  },
  data() {
    return {
      searchValue: "", // 用户搜索关键词
      domain: this.$route.query.domain || "",
      ip: "", // 用户点击IP详情
      port: "", // IP对应当前端口
      transport: "", // IP对应当前传输协议
      searchWay: "", // 用户当前检索方式
      aggIsLoading: false, // IP聚合是否正在加载中
      aggregationData: {}, // IP聚合响应数据
      start: "", // 筛选开始时间
      end: "", // 筛选结束时间
    };
  },
  created() {
    this.searchValue = this.$route.query.query;
    this.ip = this.$route.query.ip;
    this.port = String(this.$route.query.port);
    this.transport = this.$route.query.transport;
    this.searchWay = this.$route.query.searchWay;
    this.domain = this.$route.query.domain;
  },
  destroyed() {
    // this.aggregationData.port_transport.forEach((v) => {
    //   ip_detail_status.removeIpDetail(v.port);
    // });
    ip_detail_status.removeIpDetail();
  },
  watch: {
    // 监听路由变化获取参数
    $route: {
      handler: function (to) {
        if (to.name === "details" && to.query) {
          this.ip = to.query.ip;
          this.port = String(to.query.port);
          this.transport = to.query.transport;
          this.domain = this.$route.query.domain;
        }
      },
    },
  },
};
</script>

<style scoped lang="scss">
.cursor-pointer {
  cursor: pointer;
}
#IPDetails {
  position: absolute;
  background-color: rgb(247, 249, 252);
  height: calc(100% - 110px);
  width: 100%;
  box-sizing: border-box;
  padding: 12px 32px 20px 32px;
}
.resultList {
  display: flex;
  margin-top: 12px;
  height: calc(100% - 32px);
}
</style>
