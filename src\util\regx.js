// IP检验规则：可以带掩码也可以不带掩码
export function testIp(ip) {
  let rgx = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(\/\d{1,2})*$/;
  // let rgx2 = /^1(((0|27)(.(([1-9]?|1[0-9])[0-9]|2([0-4][0-9]|5[0-5])))|(72.(1[6-9]|2[0-9]|3[01])|92.168))(.(([1-9]?|1[0-9])[0-9]|2([0-4][0-9]|5[0-5]))){2})/;
  // let rgx = /^((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))(\.((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))){3}$/; 
  // let rgx3= /^(?!^255(\.255){3}$)(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])(\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)){2}\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$/
  let rgx_seg = /.+\/(\d|([1-2]\d)|(3[0-2]))$/;
  let rgx_domain=/^([a-zA-Z0-9]([a-zA-Z0-9-_]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,11}$/;
  // return (rgx.test(ip) && !rgx2.test(ip) && rgx3.test(ip)) || rgx_domain.test(ip) || (rgx.test(ip) && !rgx2.test(ip) && rgx_seg.test(ip));
  return (rgx.test(ip)) || (rgx.test(ip) && rgx_seg.test(ip)) || rgx_domain.test(ip);
}

// 邮箱校验规则，可以有用户名，也可以只有邮箱后缀
export function emailRgx(email) {
  let rgx1 =
    /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5}$/;
  let rgx2 = /^@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5}$/;
  return rgx1.test(email) || rgx2.test(email);
}

// 域名检验规则
/*
 * 每个标签可组成的字符是 - a-z A-Z 0-9，但是 - 不可作为开头，标签总长度 1-63 个字符，于是[a-zA-Z0-9][-a-zA-Z0-9]{0,62}，即首字不含 -，后面的字可以包含
 * 加上零宽断言 ^.{3,255}$，匹配开头和结尾，中间任意字符但长度在 3-255 之间 -*/
export function testDomain(domain) {
  let rgx =
    /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
  return rgx.test(domain);
}

// url检验规则
export function testUrl(url) {
  let rgx =
    /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$/;
  return rgx.test(url);
}

export function testSubject(value) {
  let rgx = /(\w+)=([\w\s*.]+)/g;
  return value.match(rgx);
}


 