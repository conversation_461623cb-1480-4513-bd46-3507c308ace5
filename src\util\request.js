import axios from "axios"; // 引入axios
import { message } from "ant-design-vue";

// 设置默认的请求超时时间。例如超过了60s，就会告知用户当前请求超时，请刷新等。
const instance = axios.create({
    timeout: 60 * 1000,
    headers: {
        "Access-Control-Allow-Origin": "*",
    },
});

// 正在进行中的请求列表
let pendingRequest = []

/**
 * 阻止重复请求
 * @param {array} pendingRequest - 请求缓存列表
 * @param {string} url - 当前请求地址
 * @param {object} params - 当前请求参数
 * @param {function} cancel - 请求中断函数
 */
const stopRepeatRequest = function (pendingRequest, url, params, cancel) {
    for (let i = 0; i < pendingRequest.length; i++) {
        // 1.当前请求和正在请求url和params完全相同时取消上一个请求
        // 2.获取资产列表数据请求时，参数params不同重新请求时取消此前所有同接口请求
        // 3.获取资产统计数据请求时，参数params中的query不同重新请求时取消此前所有相同接口请求
        if (
            (pendingRequest[i].url === url && pendingRequest[i].params === params) 
            || (pendingRequest[i].url === '/api/v1/search' && url === '/api/v1/search') 
            || (pendingRequest[i].url === '/api/v1/search/total' && url === '/api/v1/search/total' && params.query !== pendingRequest[i].params.query)
        ) {
            const cancelToken = pendingRequest[i].cancel;
            cancelToken(pendingRequest[i]);
            pendingRequest.splice(i, 1);
        }
    }
    let request = {
        url,
        params,
        cancel
    }
    pendingRequest.push(request)
}

// 请求拦截器
instance.interceptors.request.use(
    async function (config) {
        let cancel;
        config.cancelToken = new axios.CancelToken(function (c) {
            cancel = c
        })
        // 阻止重复请求。当上个请求未完成时，相同的请求不会进行
        stopRepeatRequest(pendingRequest, config.url, config.params, cancel);
        return config;
    },
    function (error) {
        console.log("error", error);
    }
);


// 响应拦截器
instance.interceptors.response.use(
    response => {
        // 如果返回的状态码为200或者304，说明接口请求成功，可以正常拿到数据
        // 否则的话抛出错误
        if (response.status === 200 || response.status === 304) {
            // console.log("response",response)
        } else if (response.status === 500 || response.status === 502) {
            message.error(
                '服务器错误',
                2
            )
        }
        // 获取请求的api
        const request = JSON.stringify(response.config.url)
        // 请求完成后，将此请求从请求列表中移除
        pendingRequest.splice(pendingRequest.findIndex(el => el === request), 1)
        return response;
    },
    // 服务器状态码不是2开头的的情况
    // 下面列举几个常见的操作，其他需求可自行扩展
    error => {
        if (error.code === 'ECONNABORTED' || error.message.indexOf('timeout') !== -1) {
            message.error(
                '网络超时',
                2
            )
        }
        return Promise.reject(error);
    }
);

export default instance;