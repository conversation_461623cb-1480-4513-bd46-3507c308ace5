import Vue from "vue";
import VueRouter from "vue-router";
import docTitleReplacer from "./hook/docTitleReplacer";
const Layout = () => import("../components/Layout.vue");
const Home = () => import("../views/Home.vue");
const Search = () => import("@/views/Search" /* webpackChunkName: "search" */);
const IPOwner = () => import("@/views/IPOwner");
const IPAggregationDetails = () => import("@/views/IPAggregationDetails");
const Page404 = () => import("../views/Page404.vue");
Vue.use(VueRouter);
const routes = [
  {
    path: "",
    name: "index",
    component: Layout,
    redirect: "/",
    children: [
      {
        path: "/",
        name: "home",
        meta: {
          title: "首页",
        },
        component: Home,
      },
      {
        path: "/search",
        name: "search",
        meta: {
          title: "搜索",
          loginRequired: true,
        },
        component: Search,
      },
      {
        path: "/details",
        name: "details",
        meta: {
          title: "IP详情",
          loginRequired: true,
        },
        component: IPAggregationDetails,
      },
      // {
      //   path: "/register",
      //   name: "register",
      //   meta: {
      //     title: "注册",
      //   },
      //   component: Register,
      // },
      // {
      //   path: "/map",
      //   name: "map",
      //   meta: {
      //     title: "地图",
      //   },
      //   component: BaiduMap,
      // },
      // {
      //   path: "/owner1",
      //   name: "owner1",
      //   meta: {
      //     title: "IP归属",
      //   },
      //   component: IPOwnership,
      // },
      {
        path: '/owner',
        name: "owner",
        meta: {
          title: "IP归属查询",
          loginRequired: true,
        },
        component: IPOwner,
      },
      {
        path: "/404",
        name: "404",
        meta: {
          title: "404",
        },
        component: Page404,
      },
    ],
  },
  { path: "*", redirect: "/404" },
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
  scrollBehavior(to, from, savedPosition) {
    return savedPosition || { x: 0, y: 0 };
  },
});
router.afterEach(docTitleReplacer);
// router.beforeEach(routerPermission);
export default router;
