import request from "@/util/request";

// 作战地图查询
/*
 * query	必须	查询语句（base64）,字段名有ip、owner、name、mail
 * */
export function getBaiduMap(params) {
  let url = "/api/v1/map/search";
  return request.get(url, { params: params });
}

// 红点查询
/*
 * ip 必须 ip值
 * */
export function getRedPoint(params) {
  let url = "/api/v1/map/point";
  return request.get(url, { params: params });
}

// 获取IP拓扑
/*
 * ip	必须	ipv4格式*/
export function getIPTopology(params) {
  let url = "/api/v1/map/topology";
  return request.get(url, { params: params });
}

// 同源IP查找
/*
 * ip	必须	ip地址  ipv4格式 */
export function getHomogeneousIP(params) {
  let url = "/api/v1/map/homologous";
  return request.get(url, { params: params });
}

//暴露面下载
export function exposeData(data) {
  let url = "/api/v1/map/expose";
  return request.post(url, data, { responseType: "blob" });
}

//关键词自动补全 api/v1/map/tips?keyword=nsfocus
export function exposeTips(params) {
  let url = "/api/v1/map/tips";
  return request.get(url, { params: params });
}
