<template>
	<div class="bg">
		<div id="home">
			<!-- 搜索框 -->
			<SearchBar/>
		</div>
	</div>
</template>

<script>
import SearchBar from "@/components/SearchBar";

export default {
	name: "Home",
	components: {
		SearchBar,
	},
};
</script>

<style scoped lang="scss">
.bg {
	position: absolute;
	box-sizing: border-box;
	display: flex;
	height: calc(100% - 100px);
	width: 100%;
	background-image: url("@/assets/img/home_bg.jpg");
	z-index: 0;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
}
#home {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	width: 100%;
	margin-left: 50%;
	padding: 0 5%;
	z-index: 2;
}
</style>