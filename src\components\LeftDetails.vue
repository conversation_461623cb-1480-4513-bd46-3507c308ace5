<template>
  <div class="leftDetails">
    <!--        显示内容加载中-->
    <div v-if="aggIsLoading" class="loadingMove">
      <a-skeleton active />
      <a-skeleton active />
    </div>
    <!--        IP端口和地理位置展示-->
    <div v-else class="IPHost">
      <!--    IP展示-->
      <div class="IPCard">
        <header style="font-weight: bold; font-size: 18px">
          <span style="color: rgb(111, 186, 44); margin-right: 8px">{{
            aggregationData.ip
          }}</span>
          <a-icon
            class="cursor-pointer"
            type="copy"
            v-clipboard:copy="aggregationData.ip"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
          />
        </header>
      </div>
      <!--    web服务-->
      <div class="IPCard">
        <header
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            margin-bottom: 12px;
          "
        >
          {{ $t("search.web_service") }}
          <!--       选择时间下拉菜单-->
          <a-range-picker
            show-time
            :ranges="{
              最近1个月: [
                moment().startOf('day').subtract(1, 'months'),
                moment(),
              ],
              最近半年: [
                moment().startOf('day').subtract(6, 'months'),
                moment(),
              ],
              最近一年: [
                moment().startOf('day').subtract(1, 'years'),
                moment(),
              ],
            }"
            :default-value="[this.aggTime.start, this.aggTime.end]"
            format="YYYY-MM-DD"
            @ok="timeComfire"
            @change="timeChange"
            style="width: 200px; display: inline-block"
          />
        </header>
        <!-- 端口展示 -->
        <button
          class="IPPort"
          v-for="(item, index) in aggregationData.port_transport"
          :class="String(item.port) === port ? 'IPPortActive' : ''"
          :key="index"
          @click="portDetail(item)"
        >
          {{ item.port }}
        </button>
      </div>
      <!--    地理位置-->
      <div class="IPCard">
        <header style="font-weight: bold; margin-bottom: 16px">
          {{ $t("search.location") }}
        </header>
        <div class="IPLocation">
          <span class="itemKey">ASN:</span>
          <span class="itemValue">{{
            aggregationData.location.asn || "-"
          }}</span>
        </div>
        <div class="IPLocation">
          <span class="itemKey">ISP:</span>
          <span class="itemValue">{{
            aggregationData.location.isp || "-"
          }}</span>
        </div>
        <div class="IPLocation">
          <span class="itemKey">{{ $t("search.longitude") }}:</span>
          <span class="itemValue">{{
            aggregationData.location.longitude || "-"
          }}</span>
        </div>
        <div class="IPLocation">
          <span class="itemKey">{{ $t("search.latitude") }}:</span>
          <span class="itemValue">{{
            aggregationData.location.latitude || "-"
          }}</span>
        </div>
        <div class="IPLocation">
          <span class="itemKey">{{ $t("search.region") }}:</span>
          <span class="itemValue">
            <!--    国旗图标-->
            <span
              :class="
                'fi fi-' + aggregationData.location.country_code.toLowerCase()
              "
              style="margin-right: 4px"
            ></span>
            <span
              >{{ aggregationData.location.region }}/{{
                aggregationData.location.city
              }}</span
            >
          </span>
        </div>
        <div class="IPLocation">
          <span class="itemKey">
            {{ $t("search.application_scenarios") }}:
          </span>
          <span class="itemValue">
            {{ aggregationData.location.usage || "-" }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { onCopySuccess, onCopyError } from "@/util/tool";
import { getIPAggregation } from "@/api/search";
import moment from "moment/moment";
export default {
  name: "LeftDetails",
  props: ["ip", "port", "transport"],
  data() {
    return {
      aggregationdata: null,
      aggregationData: {},
      aggIsLoading: false, // IP聚合是否正在加载中
      start: "", // 筛选开始时间
      end: "", // 筛选结束时间
      aggTime: {
        start: "",
        end: "",
      },
    };
  },
  watch: {
    aggTime: {
      handler() {
        let {start, end} = this.aggTime;
        this.getIPAggregationData(
          this.ip,
          this.port,
          this.transport,
          start,
          end
        );
      },
      deep: true,
    },
  },
  methods: {
    moment,
    onCopySuccess,
    onCopyError,
    // 获取IP聚合数据
    async getIPAggregationData(
      q,
      port,
      transport,
      start = this.start,
      end = this.end
    ) {
      this.aggIsLoading = true;
      getIPAggregation(q, {
        port: port,
        transport: transport,
        start_time: start,
        end_time: end,
      })
        .then((res) => {
          if (res.data.code == 10005) {
            this.$message.info(`transport只能为TCP,UDP,tcp,udp 或空`, 3);
          } else if (res.data.code == 0) {
            this.aggregationdata = res.data.data;
            this.aggregationData = this.aggregationdata;
            this.aggIsLoading = false;
          }
        })
        .catch((err) => {
          console.log(err);
          this.aggIsLoading = false;
        });
    },
    // 跳转查看对应端口详情
    portDetail(value) {
      // 切换当前port
      // 将当前路由里面的port设置为切换后的port且不发生路由跳转
      this.$router.push({
        query: {
          ip: this.ip,
          port: String(value.port),
          transport: value.transport,
          time: new Date().getTime(), // 当前时间戳
        },
      });
      // this.getIPDetailData(this.ip, this.port, this.transport, this.domain);
    },
    // 确定时间范围
    timeComfire(dateString) {
      const timeFormat = "YYYY-MM-DD";
      // this.start = dateString[0].format(timeFormat);
      // this.end = dateString[1].format(timeFormat);
      this.aggTime.start = dateString[0].format(timeFormat);
      this.aggTime.end = dateString[1].format(timeFormat);
      // this.getIPAggregationData(
      //   this.ip,
      //   this.port,
      //   this.transport,
      //   this.start,
      //   this.end
      // );
    },
    // 清除时间
    timeChange(dateString) {
      if (!dateString[0] && !dateString[1]) {
        // 获取当前时间和三个月前的时间作为默认请求时间范围
        this.getDefualtTimeRange(6);
        // this.getIPAggregationData(
        //   this.ip,
        //   this.port,
        //   this.transport,
        //   this.start,
        //   this.end
        // );
      }
    },
    // 获取当前时间和几个月前的时间作为默认请求时间范围,参数n：几个月
    getDefualtTimeRange(n) {
      this.start = moment()
        .startOf("day")
        .subtract(n, "months")
        .format("YYYY-MM-DD");
      this.end = moment().format("YYYY-MM-DD");
    },
  },
  created() {
    // 获取当前时间和半年前的时间作为默认请求时间范围
    this.getDefualtTimeRange(6);
    this.aggTime = {
      start: this.start,
      end: this.end,
    };
    this.getIPAggregationData(
      this.ip,
      this.port,
      this.transport,
      this.start,
      this.end
    );
  },
};
</script>
<style scoped lang="scss">
.loadingMove {
  width: 100%;
  padding: 40px 76px 0 28px;
}
.leftDetails {
  flex-basis: 350px;
  margin-right: 28px;
  height: 100%;
  border-radius: 8px;
  background-color: white;

  .IPHost {
    .IPCard {
      padding: 20px 16px;
      border-bottom: 1px solid rgba(229, 233, 241, 0.4);

      .IPPort {
        width: 48px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
        color: #000;
        background: #f7f8fb;
        border: 1px solid #f7f8fb;
        margin: 0 8px 8px 0;
      }

      .IPPortActive {
        background-color: rgb(59, 179, 194);
        color: white;
      }

      .IPLocation {
        display: flex;
        justify-content: space-between;
        line-height: 28px;
      }
    }
  }
}
</style>
