<template>
  <a-modal
      :visible="personnelModalVisible"
      @cancel="personnelClose"
      width="1000px"
      height="500px"
      :footer="null"
  >
    <template slot="title">
      人员情报
      <a-icon type="team" />
    </template>
    <div class="personnel">
      <a-table :columns="columns" :data-source="personnelData" size="small" :pagination="false" :scroll="{ x: true}">
        <span slot="social" slot-scope="record">
          <span v-show="record.facebook_url!==''">
            <a-icon type="facebook"/>
            {{record.facebook_url}}
             <br />
          </span>
          <span v-show="record.twitter_url!==''">
            <a-icon type="twitter"/>
            {{record.twitter_url}}
            <br />
          </span>
          <span v-show="record.github_url!==''">
            <a-icon type="github"/>
            {{record.github_url}}
          </span>
          <span v-show="record.facebook_url==='' && record.twitter_url==='' && record.github_url===''">
            无
          </span>
        </span>
        <span slot="mobile_phone">
          手机
          <a-icon type="mobile" />
        </span>
        <span slot="work_email">
          工作邮箱
          <a-icon type="mail" />
        </span>
        <span slot="mail" slot-scope="text">
          <pre>{{text}}</pre>
        </span>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: "Personnel",
  props: {
    personnelModalVisible: {
      type: Boolean,
      default: false
    },
    personnelInformation: {
      type: Array,
    },
  },
  watch: {
    personnelInformation: {
      handler: function (newValue) {
        this.personnelData = [];
        if (newValue.length > 0) {
          newValue.forEach((item, index) => {
            // 将所有社交帐号合并起来
            item.social = {
              'facebook_url': item.facebook_url,
              'twitter_url': item.twitter_url,
              'github_url': item.github_url
            }
            delete item.facebook_url;
            delete item.twitter_url;
            delete item.github_url;
            // 将邮箱1、2、3合并为其他邮箱
            item.mail = item.mail1 + '\n' + item.mail2 + '\n' + item.mail3;
            delete item.mail1;
            delete item.mail2;
            delete item.mail3;
            Object.keys(item).forEach((key) => {
              if (item[key] === '') {
                item[key] = '无';
              }
            })
            item.key = index;
            this.personnelData.push(item);
          })
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          width: '120px',
          fixed: 'left',
          align: 'center'
        },
        {
          title: '性别',
          dataIndex: 'gender',
          width: '80px',
          align: 'center'
        },
        {
          title: '出生年份',
          dataIndex: 'birth_year',
          width: '50px',
          align: 'center'
        },
        {
          title: '领英ID',
          dataIndex: 'linkedin_id',
          align: 'center'
        },
        {
          title: '社交账号',
          dataIndex: 'social',
          scopedSlots: {customRender: 'social'},
          align: 'center'
        },
        {
          dataIndex: 'work_email',
          slots: {title: 'work_email'},
          align: 'center'
        },
        {
          dataIndex: 'mobile_phone',
          slots: {title: 'mobile_phone'},
          align: 'center'
        },
        {
          title: '职位',
          dataIndex: 'job_title',
          align: 'center'
        },
        {
          title: '公司',
          dataIndex: 'company',
          align: 'center'
        },
        {
          title: '其他邮箱',
          dataIndex: 'mail',
          scopedSlots: {customRender: 'mail'},
          align: 'center'
        },
      ],
      personnelData: [],
    }
  },
  methods: {
    personnelClose() {
      this.$emit("closePersonnelModal")
    },
  },
  mounted() {

  }
}
</script>

<style scoped lang="scss">
.personnel {
  height: 500px;
  overflow: auto;
}
</style>