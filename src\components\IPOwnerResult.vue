<template>
  <div class="ResultContent">
    <SingleIPOwner
      v-if="this.searchWay === 'single'"
      class="Result"
      :data="single_data"
    />
    <MultiIPOwner
      v-if="this.searchWay === 'multi'"
      class="Result"
      :data="multi_data"
    />
  </div>
</template>
<script>
import SingleIPOwner from "./SingleIPOwner.vue";
import MultiIPOwner from "./MultiIPOwner.vue";
import { getAttribution } from "@/api/search";
import { getMultiAttribution } from "@/api/search";
import {
  ip_single_owner_status,
  ip_multi_owner_status,
} from "@/util/sessionUtils";

export default {
  name: "IPOwnerResult",
  components: {
    SingleIPOwner,
    MultiIPOwner,
  },
  data() {
    return {
      type_list: {
        1: "IP反解析域名",
        2: "绿盟信息库",
        3: "网页显示 ICP",
        4: "证书 许可组织",
        5: "证书 许可域名",
        6: "网页ICON",
        7: "网页标题",
        8: "网页copyright",
        9: "商业库A",
      },
      searchWay: "",
      single_data: {
        loading: true,
        ip: "",
        recommend: "",
        other: [],
      },
      multi_data: {
        data: null,
        loading: true,
      },
    };
  },
  methods: {
    onSingleSearch(ip) {
      this.single_data.loading = true;
      getAttribution({ target: ip })
        .then((value) => {
          const { data } = value;
          //todo: 错误码
          if (data.owner === "") {
            this.single_data.owner = "";
          } else {
            this.single_data.owner = data.owner;
            this.single_data.usage = data.usage;
            this.single_data.location = data.location;
            this.single_data.other = data.detail;
            this.single_data.other.forEach((v) => {
              v.type = this.type_list[v.type];
            });
            this.single_data.loading = false;
          }
        })
        .catch((err) => {
          console.log(err);
          this.single_data.loading = false;
        });
      // this.single_data.other = [
      //   {
      //     type: "IP反解析域名",
      //     owner: "福建锐利信息科技发展有限公司",
      //     evident:
      //       "fjrlkj.com-闽ICP备16025211号-1-福建锐利信息科技发展有限公司",
      //     tag: 1,
      //   },
      //   {
      //     type: "商业库A",
      //     owner: "福建锐利信息科技发展有限公司",
      //     evident: "福建锐利信息科技发展有限公司",
      //     tag: 2,
      //   },
      // ];

    },
    onMultiSearch(ips) {
      this.multi_data.loading = true;
      getMultiAttribution(ips)
        .then((value) => {
          const keys = Object.keys(value.data);
          let data = [];
          if (keys.length > 0) {
            keys.forEach((v, idx) => {
              data.push({
                key: idx,
                ip: v,
                owner: value.data[v],
              });
            });
            this.multi_data.data = data;
          } else {
            this.$message.err("没有找到信息！");
          }
        })
        .catch((err) => {
          console.log(err);
          this.$message.error(err);
        })
        .finally(() => {
          this.multi_data.loading = false;
        });
    },
  },
  mounted() {
    if (this.$route.query.searchWay) {
      this.searchWay = this.$route.query.searchWay;
      if (this.searchWay === "single") {
        let ip = ip_single_owner_status.getIPSingle();
        if (ip) {
          //存在需要查询的ip
          this.onSingleSearch(ip);
        }
      }
      if (this.searchWay === "multi") {
        let ips = ip_multi_owner_status.getIPMulti();
        if (ips) {
          this.onMultiSearch(ips);
        }
      }
    }
  },
  watch: {
    $route: {
      handler: function (to) {
        if (to.name === "owner") {
          if (to.query.time) {
            //有时间，则证明发起了查询
            this.searchWay = to.query.searchWay;
            if (this.searchWay === "single") {
              let ip = ip_single_owner_status.getIPSingle();
              if (ip) {
                //存在需要查询的ip
                this.onSingleSearch(ip);
              }
            }
            if (this.searchWay === "multi") {
              let ips = ip_multi_owner_status.getIPMulti();
              if (ips) {
                ips = ips.split(",");
                this.onMultiSearch(ips);
              }
            }
          } else {
            //只是标签页切换
            this.searchWay = "";
          }
        }
      },
      // immediate: true,
    },
  },
};
</script>
<style scoped lang="scss">
.ResultContent {
  width: 80%;
  height: 100%;
  margin-top: 20px;
  background-color: #fff;
}
.Result {
  width: 95%;
  margin: 50px auto;
  // overflow: scroll;
}
</style>
