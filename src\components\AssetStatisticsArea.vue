<template>
  <div class="assetStatistics">
    <a-button
      v-show="!all && ips !== 0"
      type="primary"
      size="small"
      ghost
      style="position: absolute; top: 16px; right: 20px"
      @click="getAllRank"
    >
      {{ $t("search.all") }}
    </a-button>
    <!--        独立IP数-->
    <div class="resultArea">
      <div class="areaTitle">
        {{ $t("search.distinct_ips") }}
      </div>
      <div v-show="ipAndPortIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!ipAndPortIsLoading && ips !== 0">
        <div class="areaList">
          <span class="itemKey" style="font-weight: bold">{{ ips }}</span>
        </div>
      </div>
      <a-empty v-show="!ipAndPortIsLoading && ips === 0" />
    </div>
    <!-- <IPStatisticsPart :ips="ips" :ipAndPortIsLoading="ipAndPortIsLoading" :styles="ip_styles"/> -->
    <!--        地区分布-->
    <div class="resultArea">
      <div class="areaTitle">
        {{ $t("search.distribution") }}
      </div>
      <div v-show="countryIsLoading">
        <a-skeleton active />
      </div>
      <div
        v-if="
          !countryIsLoading &&
          (countryMap.length > 0 || chinaMapList.length > 0)
        "
        style="position: relative"
      >
        <div style="position: absolute; right: 0; top: -38px">
          <a-radio-group v-model="mapType" defaultValue="世界" size="small">
            <a-radio-button value="世界">
              {{ $t("search.world") }}
            </a-radio-button>
            <a-radio-button value="中国" :defaultChecked="true">
              {{ $t("search.china") }}
            </a-radio-button>
          </a-radio-group>
        </div>
        <ChinaMap v-show="mapType === '中国'" :china-map-list="chinaMapList" />
        <WorldMap v-show="mapType === '世界'" :world-map-list="countryMap" />
      </div>
      <a-empty
        v-show="
          !countryIsLoading &&
          countryMap.length === 0 &&
          chinaMapList.length === 0
        "
      />
    </div>
    <!--        国家排行-->
    <div class="resultArea">
      <div class="areaTitle">
        {{ $t("search.countryRank") }}
      </div>
      <div v-show="countryIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!countryIsLoading && countryRank.length > 0">
        <a-collapse :bordered="false">
          <template #expandIcon="props">
            <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0" />
          </template>
          <a-collapse-panel
            v-for="(item, index) in countryRank"
            :key="index + 1"
            style="
              background: rgba(165, 212, 173, 0.1);
              border-radius: 4px;
              margin-bottom: 4px;
              border: 0;
              overflow: hidden;
            "
          >
            <template slot="header">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  line-height: 20px;
                "
              >
                <span
                  style="cursor: pointer"
                  @click="addTag('country_code', item.name)"
                >
                  <span :class="'fi fi-' + item.name"></span>
                  <span style="margin-left: 4px">{{ item.zhcn_name }}</span>
                </span>
                <span class="itemValue">{{ item.count.toLocaleString() }}</span>
              </div>
            </template>
            <div v-for="(i, index) in item.region" :key="index">
              <p
                style="
                  display: flex;
                  justify-content: space-between;
                  line-height: 24px;
                  margin: 0;
                  cursor: pointer;
                "
                @click="addTag('region', i.name)"
              >
                <span class="itemKey">{{
                  i.name || $t("search.unknown")
                }}</span>
                <span class="itemValue">{{ i.count.toLocaleString() }}</span>
              </p>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <a-empty v-show="!countryIsLoading && countryRank.length === 0" />
    </div>
    <!--        端口排名-->
    <div class="resultArea">
      <div class="areaTitle">
        {{ $t("search.portRank") }}
      </div>
      <div v-show="ipAndPortIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!ipAndPortIsLoading && portRank.length > 0">
        <div class="areaList" v-for="(item, index) in portRank" :key="index">
          <a-tag
            color="blue"
            style="
              cursor: pointer;
              margin-left: 16px;
              height: 20px;
              line-height: 20px;
            "
            @click="addTag('port', item.name)"
          >
            {{ item.name }}
          </a-tag>
          <span class="itemValue">{{ item.count.toLocaleString() }}</span>
        </div>
      </div>
      <a-empty v-show="!ipAndPortIsLoading && portRank.length === 0" />
    </div>
    <!--        资产标签排名-->
    <div class="resultArea">
      <div class="areaTitle">
        {{ $t("search.assetTagRank") }}
      </div>
      <div v-show="ipAndPortIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!ipAndPortIsLoading && assetTagRank.length > 0">
        <div
          class="areaList"
          v-for="(item, index) in assetTagRank"
          :key="index"
        >
          <a-tag
            color="green"
            style="
              margin-left: 16px;
              height: 20px;
              line-height: 20px;
              cursor: pointer;
            "
            @click="addTag('asset_tags', item.name)"
          >
            {{ item.name }}
          </a-tag>
          <span class="itemValue">{{ item.count.toLocaleString() }}</span>
        </div>
      </div>
      <a-empty v-show="!ipAndPortIsLoading && assetTagRank.length === 0" />
    </div>
    <!--        标题排名-->
    <div v-show="all" class="resultArea">
      <div class="areaTitle">
        {{ $t("search.titleRank") }}
      </div>
      <div v-show="restAssetIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!restAssetIsLoading">
        <div class="areaList" v-for="(item, index) in titleRank" :key="index">
          <span
            :title="item.name"
            class="itemKey"
            style="width: 200px; color: #0072ee; cursor: pointer"
            @click="addTag('title', item.name)"
            >{{ item.name }}</span
          >
          <span class="itemValue">{{ item.count.toLocaleString() }}</span>
        </div>
      </div>
      <a-empty v-show="!restAssetIsLoading && titleRank.length === 0" />
    </div>
    <!--        组件排名-->
    <div v-show="all" class="resultArea">
      <div class="areaTitle">
        {{ $t("search.componentRank") }}
      </div>
      <div v-show="restAssetIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!restAssetIsLoading">
        <div
          class="areaList"
          v-for="(item, index) in componentRank"
          :key="index"
        >
          <a-tag
            title="item.name"
            class="itemKey"
            style="cursor: pointer"
            @click="addTag('server', item.name)"
          >
            {{ item.name }}
          </a-tag>
          <span class="itemValue">{{ item.count.toLocaleString() }}</span>
        </div>
      </div>
      <a-empty v-show="!restAssetIsLoading && componentRank.length === 0" />
    </div>
    <!--        协议排名-->
    <div v-show="all" class="resultArea">
      <div class="areaTitle">
        {{ $t("search.protocolRank") }}
      </div>
      <div v-show="restAssetIsLoading">
        <a-skeleton active />
      </div>
      <div v-show="!restAssetIsLoading">
        <div
          class="areaList"
          v-for="(item, index) in protocolRank"
          :key="index"
        >
          <a-tag
            color="orange"
            class="itemKey"
            style="cursor: pointer"
            @click="addTag('protocol', item.name)"
          >
            {{ item.name }}
          </a-tag>
          <span class="itemValue">{{ item.count }}</span>
        </div>
      </div>
      <a-empty v-show="!restAssetIsLoading && protocolRank.length === 0" />
    </div>
  </div>
</template>

<script>
import ChinaMap from "@/components/ChinaMap";
import WorldMap from "@/components/WorldMap";
import { getAssetStatistics } from "@/api/search";
export default {
  name: "assetStatisticsArea",
  components: {
    ChinaMap,
    WorldMap,
  },
  props: ["startTime", "endTime", "searchValue"],
  emits: ["addtag"],
  data() {
    // const resultArea = {
    //     'width': '100%',
    //     'padding': '16px 20px',
    //     'border-bottom': '1px solid #eaedf2'
    // }
    // const areaTitle = {
    //     'color': '#26323e',
    //     'font-size': '18px',
    //     'line-height': '26px',
    //     'font-weight': '700',
    //     'margin-bottom': '12px'
    // }
    // const areaList = {
    //     'display': 'flex',
    //     'justify-content': 'space-between',
    //     'align-items': 'center',
    //     'line-height': '28px',
    // }
    // const itemValue = {
    //     'color': 'rgb(37, 37, 37)',
    //     'margin-right': '16px',
    // }
    return {
      all: false,
      countryIsLoading: true, // 资产统计的国家是否在加载中
      ipAndPortIsLoading: true, // 资产统计的ip和port是否在加载中
      restAssetIsLoading: true, // 剩余的资产统计是否在加载中
      mapType: "世界", // 显示地图类型
      ips: 0, // 独立IP数
      countryRank: [], // 国家排名
      countryMap: [], // 世界地图国家渲染数据
      chinaMapList: [], // 中国地图省份渲染数据
      portRank: [], // 端口排名
      assetTagRank: [], //资产标签排名
      assetIndustryRank: [], //资产行业排名
      titleRank: [], // 标题排名
      componentRank: [], // 组件排名
      protocolRank: [], // 协议排名
      iconRank: [], // icon排名
      // child_styles: {resultArea, areaTitle, areaList, itemValue}, //独立IP数的styles
    };
  },
  methods: {
    determineCode(code) {
      if (code == 10002) {
        // 正常不会出现
        this.$message.info(`每页数据量超限, 最大值200`, 3);
      } else if (code == 10003) {
        this.$message.info(`数量超限, 最多可查看10000条数据`, 3);
      } else if (code == 10004) {
        this.$message.info(`输入值格式有误, 请排查语句标点符号等`, 3);
      } else if (code == 0) {
        return true;
      }
      return false;
    },
    // 获取资产统计数据
    getAssetStatisticsData(
      q,
      all = false,
      start = this.startTime,
      end = this.endTime
    ) {
      if (!all) {
        this.ipAndPortIsLoading = true;
        this.countryIsLoading = true;
        // 获取ip和port的资产统计数据
        getAssetStatistics({
          query: btoa(unescape(encodeURIComponent(q))),
          fields: "ip,port,asset_industry,asset_tag",
          start_time: start,
          end_time: end,
        })
          .then((res) => {
            if (this.determineCode(res.data.code)) {
              this.ips = res.data.distinct_ips.toLocaleString();
              this.portRank = res.data.rank.port || [];
              this.assetTagRank = res.data.rank.asset_tag || [];
              this.assetIndustryRank = res.data.rank.asset_industry || [];
            } else {
              this.ips = 0;
              this.portRank = [];
              this.assetTagRank = [];
              this.assetIndustryRank = [];
            }
            this.ipAndPortIsLoading = false;
          })
          .catch((err) => {
            this.ips = 0;
            this.portRank = [];
            this.assetTagRank = [];
            this.assetIndustryRank = [];
            this.ipAndPortIsLoading = false;
            console.log("getAssetStatistics err", err);
          });
        // 获取国家资产统计数据
        getAssetStatistics({
          query: btoa(unescape(encodeURIComponent(q))),
          fields: "country,country_chart,china_chart",
          start_time: start,
          end_time: end,
        })
          .then((res) => {
            if (this.determineCode(res.data.code)) {
              this.countryRank = res.data.rank.country || [];
              this.countryMap = res.data.rank.country_chart || [];
              this.chinaMapList = res.data.rank.china_chart || [];
            } else {
              this.countryRank = [];
              this.countryMap = [];
              this.chinaMapList = [];
            }
            this.countryIsLoading = false;
          })
          .catch((err) => {
            this.countryRank = [];
            this.countryMap = [];
            this.chinaMapList = [];
            this.countryIsLoading = false;
            console.log("getAssetStatistics err", err);
          });
      } else {
        this.restAssetIsLoading = true;
        // 获取剩下的资产统计数据
        getAssetStatistics({
          query: btoa(unescape(encodeURIComponent(q))),
          fields: "server,title,protocol",
          start_time: start,
          end_time: end,
        })
          .then((res) => {
            if (this.determineCode(res.data.code)) {
              this.componentRank = res.data.rank.server || [];
              this.titleRank = res.data.rank.title || [];
              if (res.data.rank.protocol) {
                this.protocolRank = res.data.rank.protocol.filter((item) => {
                  return item.name !== "";
                });
                for (let i = 0; i < this.protocolRank.length; i++) {
                  this.protocolRank[i].count =
                    this.protocolRank[i].count.toLocaleString();
                }
              } else {
                this.protocolRank = [];
              }

              this.iconRank = res.data.rank.icon || [];
            }
            this.restAssetIsLoading = false;
          })
          .catch((err) => {
            this.componentRank = [];
            this.titleRank = [];
            this.protocolRank = [];
            this.iconRank = [];
            this.restAssetIsLoading = false;
            console.log("getAssetStatistics err", err);
          });
      }
    },
    // 获取所有排名
    getAllRank() {
      this.all = true;
      this.getAssetStatisticsData(
        this.searchValue,
        this.all,
        this.startTime,
        this.endTime
      );
    },
    addTag(tag, text) {
      this.$emit("addtag", tag, text);
    },
  },
};
</script>
<style lang="scss" scoped>
.assetStatistics {
  flex-basis: 350px;
  flex-grow: 0;
  max-height: 100%;
  background-color: white;
  overflow-y: auto;
  margin-right: 28px;
  border-radius: 8px;
  position: relative;
  box-shadow: 9px 9px 12px rgb(189 189 189 / 20%),
    -9px -9px 12px rgb(255 255 255 / 25%);

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;

  .resultArea {
    width: 100%;
    padding: 16px 20px;
    border-bottom: 1px solid #eaedf2;

    .areaTitle {
      color: #26323e;
      font-size: 18px;
      line-height: 26px;
      font-weight: 700;
      margin-bottom: 12px;
    }

    .areaList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 28px;

      .itemKey {
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        color: rgb(87, 87, 87);
        margin-left: 16px;
      }

      .itemValue {
        color: rgb(37, 37, 37);
        margin-right: 16px;
      }
    }
  }
}
</style>
