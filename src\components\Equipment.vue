<template>
  <a-modal
      :visible="equipmentModalVisible"
      @cancel="equipmentClose"
      title="同源IP"
      width="1200px"
      height="500px"
      :footer="null"
  >
    <div class="equipment">
      <div v-if="listIsLoading" class="loadingMove">
        <a-skeleton active/>
        <a-skeleton active/>
        <a-skeleton active/>
      </div>
      <a-table v-else :columns="columns" :data-source="equipmentInfo" bordered :pagination="false" style="height: 450px">
<!--        &lt;!&ndash;      查找搜索&ndash;&gt;-->
<!--        <div-->
<!--            slot="filterDropdown"-->
<!--            slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"-->
<!--            style="padding: 8px"-->
<!--        >-->
<!--          <a-input-->
<!--              v-ant-ref="c => (searchInput = c)"-->
<!--              :placeholder="`查找${column.title}`"-->
<!--              :value="selectedKeys[0]"-->
<!--              style="width: 188px; margin-bottom: 8px; display: block;"-->
<!--              @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"-->
<!--              @pressEnter="() => handleSearch(selectedKeys, confirm, column.title)"-->
<!--          />-->
<!--          <a-button-->
<!--              type="primary"-->
<!--              icon="search"-->
<!--              size="small"-->
<!--              style="width: 90px; margin-right: 8px"-->
<!--              @click="() => handleSearch(selectedKeys, confirm, column.title)"-->
<!--          >-->
<!--            查找-->
<!--          </a-button>-->
<!--          <a-button size="small" style="width: 90px" @click="() => handleReset(clearFilters)">-->
<!--            重置-->
<!--          </a-button>-->
<!--        </div>-->
<!--        <a-icon-->
<!--            slot="filterIcon"-->
<!--            slot-scope="filtered"-->
<!--            type="search"-->
<!--            :style="{ color: filtered ? '#108ee9' : undefined }"-->
<!--        />-->
<!--        <template slot="customRender" slot-scope="text, record, index, column">-->
<!--          <span v-if="searchText && searchedColumn === column.title">-->
<!--        <template-->
<!--            v-for="(fragment, i) in text-->
<!--            .toString()-->
<!--            .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"-->
<!--        >-->
<!--          <mark-->
<!--              v-if="fragment.toLowerCase() === searchText.toLowerCase()"-->
<!--              :key="i"-->
<!--              class="highlight"-->
<!--          >{{ fragment }}</mark-->
<!--          >-->
<!--          <template v-else>{{ fragment }}</template>-->
<!--        </template>-->
<!--      </span>-->
<!--          <template v-else>-->
<!--            {{ text }}-->
<!--          </template>-->
<!--        </template>-->
        <span slot="level" slot-scope="record">
          {{ record.ch }}
        </span>
        <span slot="action" slot-scope="text">
        <a @click="addHomologous(text)">定位</a>
      </span>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
import {message} from 'ant-design-vue';
import {getHomogeneousIP} from "@/api/baiduMap";

export default {
  name: "Equipment",
  props: {
    equipmentModalVisible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      searchText: '',
      searchInput: null,
      searchedColumn: '',
      listIsLoading: true,   // 当前列表是否加载中
      columns: [
        {
          title: '序号',
          dataIndex: 'num',
          className: 'column-num',
          align: 'center'
        },
        {
          title: '设备IP',
          dataIndex: 'ip',
          filters: [],
          onFilter: (value, record) => record.ip.indexOf(value) === 0,
          align: 'center'
        },
        {
          title: '应用产品',
          dataIndex: 'product',
          scopedSlots: {customRender: 'product'},
          filters: [
            {
              text: '以中文开头的应用产品',
              value: '以中文开头的应用产品',
              children: [],
            },
            {
              text: '以a/A开头的应用产品',
              value: '以a/A开头的应用产品',
              children: [],
            },
            {
              text: '以b/B开头的应用产品',
              value: '以b/B开头的应用产品',
              children: [],
            },
            {
              text: '以c/C开头的应用产品',
              value: '以c/C开头的应用产品',
              children: [],
            },
            {
              text: '以d/D开头的应用产品',
              value: '以d/D开头的应用产品',
              children: [],
            },
            {
              text: '以e/E开头的应用产品',
              value: '以e/E开头的应用产品',
              children: [],
            },
            {
              text: '以f/F开头的应用产品',
              value: '以f/F开头的应用产品',
              children: [],
            },
            {
              text: '以g/G开头的应用产品',
              value: '以g/G开头的应用产品',
              children: [],
            },
            {
              text: '以h/H开头的应用产品',
              value: '以h/H开头的应用产品',
              children: [],
            },
            {
              text: '以i/I开头的应用产品',
              value: '以i/I开头的应用产品',
              children: [],
            },
            {
              text: '以j/J开头的应用产品',
              value: '以j/J开头的应用产品',
              children: [],
            },
            {
              text: '以k/K开头的应用产品',
              value: '以k/K开头的应用产品',
              children: [],
            },
            {
              text: '以l/L开头的应用产品',
              value: '以l/L开头的应用产品',
              children: [],
            },
            {
              text: '以m/M开头的应用产品',
              value: '以m/M开头的应用产品',
              children: [],
            },
            {
              text: '以n/N开头的应用产品',
              value: '以n/M开头的应用产品',
              children: [],
            },
            {
              text: '以o/O开头的应用产品',
              value: '以o/O开头的应用产品',
              children: [],
            },
            {
              text: '以p/P开头的应用产品',
              value: '以p/P开头的应用产品',
              children: [],
            },
            {
              text: '以q/Q开头的应用产品',
              value: '以q/Q开头的应用产品',
              children: [],
            },
            {
              text: '以r/R开头的应用产品',
              value: '以r/R开头的应用产品',
              children: [],
            },
            {
              text: '以s/S开头的应用产品',
              value: '以s/S开头的应用产品',
              children: [],
            },
            {
              text: '以t/T开头的应用产品',
              value: '以t/T开头的应用产品',
              children: [],
            },
            {
              text: '以u/U开头的应用产品',
              value: '以u/U开头的应用产品',
              children: [],
            },
            {
              text: '以v/V开头的应用产品',
              value: '以v/V开头的应用产品',
              children: [],
            },
            {
              text: '以w/W开头的应用产品',
              value: '以w/W开头的应用产品',
              children: [],
            },
            {
              text: '以x/X开头的应用产品',
              value: '以x/X开头的应用产品',
              children: [],
            },
            {
              text: '以y/Y开头的应用产品',
              value: '以y/Y开头的应用产品',
              children: [],
            },
            {
              text: '以z/Z开头的应用产品',
              value: '以z/Z开头的应用产品',
              children: [],
            },
          ],
          onFilter: (value, record) => record.product.indexOf(value) === 0,
          align: 'center'
        },
        {
          title: '厂商',
          dataIndex: 'vendor',
          scopedSlots: {customRender: 'vendor'},
          filters: [
            {
              text: '以中文开头的厂商',
              value: '以中文开头的厂商',
              children: [],
            },
            {
              text: '以a/A开头的厂商',
              value: '以a/A开头的厂商',
              children: [],
            },
            {
              text: '以b/B开头的厂商',
              value: '以b/B开头的厂商',
              children: [],
            },
            {
              text: '以c/C开头的厂商',
              value: '以c/C开头的厂商',
              children: [],
            },
            {
              text: '以d/D开头的厂商',
              value: '以d/D开头的厂商',
              children: [],
            },
            {
              text: '以e/E开头的厂商',
              value: '以e/E开头的厂商',
              children: [],
            },
            {
              text: '以f/F开头的厂商',
              value: '以f/F开头的厂商',
              children: [],
            },
            {
              text: '以g/G开头的厂商',
              value: '以g/G开头的厂商',
              children: [],
            },
            {
              text: '以h/H开头的厂商',
              value: '以h/H开头的厂商',
              children: [],
            },
            {
              text: '以i/I开头的厂商',
              value: '以i/I开头的厂商',
              children: [],
            },
            {
              text: '以j/J开头的厂商',
              value: '以j/J开头的厂商',
              children: [],
            },
            {
              text: '以k/K开头的厂商',
              value: '以k/K开头的厂商',
              children: [],
            },
            {
              text: '以l/L开头的厂商',
              value: '以l/L开头的厂商',
              children: [],
            },
            {
              text: '以m/M开头的厂商',
              value: '以m/M开头的厂商',
              children: [],
            },
            {
              text: '以n/N开头的厂商',
              value: '以n/M开头的厂商',
              children: [],
            },
            {
              text: '以o/O开头的厂商',
              value: '以o/O开头的厂商',
              children: [],
            },
            {
              text: '以p/P开头的厂商',
              value: '以p/P开头的厂商',
              children: [],
            },
            {
              text: '以q/Q开头的厂商',
              value: '以q/Q开头的厂商',
              children: [],
            },
            {
              text: '以r/R开头的厂商',
              value: '以r/R开头的厂商',
              children: [],
            },
            {
              text: '以s/S开头的厂商',
              value: '以s/S开头的厂商',
              children: [],
            },
            {
              text: '以t/T开头的厂商',
              value: '以t/T开头的厂商',
              children: [],
            },
            {
              text: '以u/U开头的厂商',
              value: '以u/U开头的厂商',
              children: [],
            },
            {
              text: '以v/V开头的厂商',
              value: '以v/V开头的厂商',
              children: [],
            },
            {
              text: '以w/W开头的厂商',
              value: '以w/W开头的厂商',
              children: [],
            },
            {
              text: '以x/X开头的厂商',
              value: '以x/X开头的厂商',
              children: [],
            },
            {
              text: '以y/Y开头的厂商',
              value: '以y/Y开头的厂商',
              children: [],
            },
            {
              text: '以z/Z开头的厂商',
              value: '以z/Z开头的厂商',
              children: [],
            },
          ],
          onFilter: (value, record) => record.vendor.indexOf(value) === 0,
          align: 'center'
        },
        {
          title: '版本',
          dataIndex: 'version',
          scopedSlots: {customRender: 'version'},
          align: 'center'
        },
        {
          title: '漏洞',
          dataIndex: 'vulnerability',
          filters: [],
          onFilter: (value, record) => record.vulnerability.indexOf(value) === 0,
          align: 'center'
        },
        {
          title: '漏洞等级',
          dataIndex: 'level',
          scopedSlots: {customRender: 'level'},
          sorter: (a, b) => a.level.num - b.level.num,
          sortDirections: ['descend', 'ascend'],
          align: 'center'
        },
        {
          title: '节点发现时间',
          dataIndex: 'date',
          scopedSlots: {customRender: 'date'},
          sorter: (a, b) => {
            return a.date > b.date ? 1 : -1
          },
          sortDirections: ['descend', 'ascend'],
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: {customRender: 'action'},
          align: 'center'
        },
      ],
      equipmentInfo: [],
    }
  },
  watch: {
    equipmentModalVisible: {
      handler: function (newValue) {
        if (newValue) {
          this.equipmentInfo = [];
          this.listIsLoading = true;
          let hide = message.loading('正在查找同源IP', 0);
          getHomogeneousIP({
            ip: sessionStorage.getItem('ip'),
          }).then((res) => {
            let homologousList = res.data.homologous;
            if (homologousList.length === 0) {
              hide();
              this.listIsLoading = false;
              this.$message.warning('该点没有同源IP');
            } else {
              let ipList = [];
              let productList = [];
              let vendorList = [];
              let bugList = [];
              let bugTimeList = [];
              homologousList.forEach((item, index) => {
                item.key = String(index + 1);
                item.num = String(index + 1);
                item.action = item.ip;
                item.date = item.date.slice(0, 10);
                item.product = item.product || '-';
                // ip
                if(ipList.indexOf(item.ip) === -1) {
                  ipList.push(item.ip);
                  this.columns[1].filters.push({
                    text: item.ip,
                    value: item.ip
                  });
                }
                // 产品
                if (item.product !== '-' && productList.indexOf(item.product) === -1) {
                  productList.push(item.product);
                  if (item.product.slice(0, 1).toUpperCase().charCodeAt() - 64 < 1 || item.product.slice(0, 1).toUpperCase().charCodeAt() - 64 > 26) {
                    this.columns[2].filters[26].children.push({
                      text: item.product,
                      value: item.product
                    });
                  } else {
                    this.columns[2].filters[item.product.slice(0, 1).toUpperCase().charCodeAt() - 64].children.push({
                      text: item.product,
                      value: item.product
                    });
                  }
                }
                // 产商
                item.vendor = item.vendor || '-';
                if (item.vendor !== '-' && vendorList.indexOf(item.vendor) === -1) {
                  vendorList.push(item.vendor);
                  if (item.vendor.slice(0, 1).toUpperCase().charCodeAt() - 64 < 1 || item.vendor.slice(0, 1).toUpperCase().charCodeAt() - 64 > 26) {
                    this.columns[3].filters[26].children.push({
                      text: item.vendor,
                      value: item.vendor
                    });
                  } else {
                    this.columns[3].filters[item.vendor.slice(0, 1).toUpperCase().charCodeAt() - 64].children.push({
                      text: item.vendor,
                      value: item.vendor
                    });
                  }
                }
                // 漏洞
                item.vulnerability = item.vulnerability || '-';
                if (item.vulnerability !== '-' && bugList.indexOf(item.vulnerability) === -1) {
                  bugList.push(item.vulnerability);
                  if (bugTimeList.indexOf(item.vulnerability.slice(4, 8)) === -1) {
                    bugTimeList.push(item.vulnerability.slice(4, 8));
                    this.columns[5].filters.push({
                      text: item.vulnerability.slice(4, 8) + '年的漏洞',
                      value: item.vulnerability.slice(4, 8) + '年的漏洞',
                      children: [
                        {
                          text: item.vulnerability,
                          value: item.vulnerability
                        }
                      ]
                    });
                  } else {
                    this.columns[5].filters.forEach((year) => {
                      if (year.text === item.vulnerability.slice(4, 8) + '年的漏洞') {
                        year.children.push({
                          text: item.vulnerability,
                          value: item.vulnerability
                        })
                      }
                    })
                  }
                }
                item.version = item.version || '-';
                switch (item.level) {
                  case 'LOW':
                    item.level = {
                      ch: 'LOW',
                      num: 1
                    };
                    break;
                  case 'HIGH':
                    item.level = {
                      ch: 'HIGH',
                      num: 2
                    };
                    break;
                  default:
                    item.level = {
                      ch: '-',
                      num: 0
                    };
                    break;
                }
              })
              // 将产品子分类下为空的分类去掉
              this.columns[2].filters.forEach((child, index) => {
                if (child.children.length < 1) {
                  this.columns[2].filters.splice(index, 1);
                }
              })
              // 将产商子分类下为空的分类去掉
              this.columns[3].filters.forEach((child, index) => {
                if (child.children.length < 1) {
                  this.columns[3].filters.splice(index, 1);
                }
              })
              // 对漏洞子分类进行排序
              let arr = this.columns[5].filters;
              let max = arr.length - 1;
              for (let j = 0; j < max; j++) {
                // 声明一个变量，作为标志位
                let done = true;
                for (let i = 0; i < max - j; i++) {
                  if (arr[i].text.slice(0,4) < arr[i + 1].text.slice(0,4)) {
                    let temp = arr[i];
                    arr[i] = arr[i + 1];
                    arr[i + 1] = temp;
                    done = false;
                  }
                }
                if (done) {
                  break;
                }
              }
              this.equipmentInfo = homologousList;
              this.listIsLoading = false;
              hide();
            }
          }).catch((err) => {
            console.log("getHomogeneousIP err", err)
          })
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 关闭当前弹窗
    equipmentClose() {
      this.$emit("closeEquipmentModal");
    },
    // 增加同源设备地图节点
    addHomologous(ip) {
      this.$emit("addHomologous", ip);
    },
    // 搜索IP
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm();
      this.searchText = selectedKeys[0];
      this.searchedColumn = dataIndex;
    },
    // 重置
    handleReset(clearFilters) {
      clearFilters();
      this.searchText = '';
    },
  },
}
</script>

<style scoped>
.loadingMove {
  width: 100%;
  margin: 24px auto;
}
</style>

<style lang="scss">
.equipment {
  height: 100%;
  overflow-y: auto;

  th.column-num,
  td.column-num {
    padding-left: 8px !important;
  }

  .ant-table-body {
    overflow-y: auto;
    overflow-x: auto;
  }
}

.ant-table-filter-dropdown {
  max-height: 400px;
  overflow-y: auto;
}

.ant-table-filter-dropdown .ant-dropdown-menu {
  max-height: calc(100% - 36px);
  overflow-y: hidden;
}


</style>