<template>
  <a-modal
      :visible="treeModalVisible"
      @cancel="treeClose"
      title="路由路径"
      width="90%"
      height="600px"
      :footer="null"
  >
    <div ref="treeEchart" style="height: 70vh;overflow-x: auto"></div>
  </a-modal>
</template>

<script>
import {init} from "echarts/core";
import {getIPTopology} from "@/api/baiduMap";
import {message} from "ant-design-vue";

export default {
  name: "IPTopology",
  props: {
    treeModalVisible: {
      type: Boolean,
      default: false
    },
    ip: {
      type: String
    },
  },
  data() {
    return {
      // 树图数据集
      treeData: [
        {
          name: '',
          label: {
            backgroundColor: '#FEF2CC',
          }
        }
      ],
      fromRouterList: [],         // 前一跳路由集合
      ipList: [],                 // 前一跳路由同源IP集合
      probeList: [],              // 探测出口节点集合
    }
  },
  mounted() {
    // 获取IP拓扑
    getIPTopology({
      ip: this.ip
    }).then((res) => {
      this.treeData[0].name = res.data.ip;     // 目标节点
      let topologys = res.data.data;
      topologys.forEach((topology) => {
        // 探测出口节点集合
        this.probeList.push(topology.probe);
        topology.path.reverse().splice(0, 1);
        let path = topology.path;
        if (topology.formrouter) {
          this.fromRouterList.push(topology.formrouter);
        } else {
          path.unshift('. . . . . .');
        }
        this.listToTopology(this.treeData[0], path, 0);
      })
      // 设置树图
      this.setChart();
      message.info(
          '支持鼠标缩放和平移',
          5,
      )
    }).catch((err) => {
      console.log("getIPTopology err", err)
    })
  },
  methods: {
    treeClose() {
      this.$emit("closeTreeModal")
    },
    setChart() {
      // 基于准备好的dom，初始化echarts实例
      let myChart = init(this.$refs.treeEchart);
      let _this = this;
      // 设置指定节点样式
      this.treeData[0].children.forEach((item) => {
        if (item.name !== '. . . . . .') {
          item.label = {
            backgroundColor: '#E2F1D4',
          };
        }
      })
      // 指定图表的配置项和数据
      let option = {
        tooltip: { //提示框组件
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: function (params) {
            let tip = params.data.name === '. . . . . .' ? '丢包或者匿名缺失' : 'IP：' + params.data.name + '<br/>';
            if (params.data.name === _this.ip) tip = '目标IP：' + params.data.name + '<br/>';
            let index = _this.fromRouterList.indexOf(params.data.name);
            if (index !== -1) {
              tip = '前一跳路由' + '<br/>';
            }
            if (_this.probeList.indexOf(params.data.name) !== -1) {
              tip = '探测出口节点';
            }
            return tip
          }
        },
        series: [{
          type: 'tree',
          initialTreeDepth: 5,
          data: this.treeData,
          width: "90%",
          height: "100%",
          top: '2%',
          left: '2%',
          bottom: '2%',
          right: '2%',
          roam: true,
          orient: "RL",
          symbolSize: 10,
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left',
            fontSize: 14,
            backgroundColor: '#fff',
            shadowColor: '#ccc',
            shadowBlur: 1,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
            borderColor: '#e6e6e6',
            borderWidth: 1,
            borderRadius: 5,
            padding: 7,
            height: 20
          },
          itemStyle: {
            color: '#A5D4AD'
          },
          lineStyle: {
            color: '#000',
            width: 0.5,
          },
          // 叶子节点样式
          leaves: {
            label: {
              backgroundColor: '#eee',
            },
          },
          // 聚焦
          emphasis: {
            focus: 'descendant',
            label: {
              borderWidth: 2,
              backgroundColor: '#e6e6e6'
            }
          },
          expandAndCollapse: false,
          animationDuration: 550,
          animationDurationUpdate: 750
        }]
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    },
    // 将ip列表转换为拓扑
    listToTopology(root, list, layer) {
      if (layer < list.length) {
        if (!('children' in root)) {
          this.$set(root, "children", []);
        }
        root.children.push({
          name: list[layer]
        })
        layer += 1
        this.listToTopology(root.children[root.children.length - 1], list, layer);
      } else if (layer === list.length) {
        this.$set(root, "children", []);
        root.children.push({
          name: this.probeList[this.probeList.length - 1]
        })
      }
    }
  },

}
</script>

