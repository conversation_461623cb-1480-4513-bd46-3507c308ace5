<template>
  <div class="rightDetails">
    <!--        显示内容加载中-->
    <div v-if="detailIsLoading" class="loadingMove">
      <a-skeleton active />
      <a-skeleton active />
      <a-skeleton active />
    </div>
    <!--        IP聚合详情内容渲染-->
    <div v-else class="IPDetails">
      <div v-show="getResult">
        <div class="IPTitle">
          <a-icon type="link" />
          <!--     IP对应的url-->
          <a :href="detailData.url" target="_blank" class="link">
            {{ detailData.url }}
          </a>
          <a-icon
            class="cursor-pointer"
            type="copy"
            v-clipboard:copy="detailData.url"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
          />
        </div>
        <div class="bannerCard">
          <!--      网站标题和最近更新时间-->
          <div class="IPBannerTop">
            <span class="webTitle">{{ detailData.title || "-" }}</span>
            <span class="lastSeen">
              <a-icon
                type="clock-circle"
                style="color: #595959; margin-right: 4px"
              />
              最近发现时间:
              {{ detailData.last_seen && detailData.last_seen.slice(0, 10) }}
            </span>
          </div>
          <!--      状态码和协议标签-->
          <div class="labelList">
            <!--     状态码-->
            <span
              v-if="detailData.status_code"
              class="IPStatus"
              :class="
                detailData.status_code === 200
                  ? 'IPSuccessStatus'
                  : 'IPErrorStatus'
              "
              >{{ detailData.status_code }}</span
            >
            <!--   协议标签-->
            <span class="label">{{
              detailData.protocol + detailData.protocol_version
            }}</span>
            <span class="label">{{ detailData.transport_protocol }}</span>
          </div>
          <!--      垂直菜单-->
          <div class="bannerContent">
            <a-tabs
              default-active-key="1"
              tab-position="left"
              :tabBarStyle="{ padding: 0 }"
              style="height: 100%"
            >
              <a-tab-pane
                key="1"
                tab="网站正文"
                v-if="!_isEmpty(detailData.banner)"
              >
                <main class="bannerContainer" style="height: 526px">
                  <!-- 一键复制按钮 -->
                  <div
                    style="
                      position: relative;
                      color: #396fff;
                      cursor: pointer;
                      text-align: right;
                      /* left: 100%; */
                    "
                    v-clipboard:copy="detailData.banner"
                    v-clipboard:success="onCopySuccess"
                    v-clipboard:error="onCopyError"
                  >
                    <div style="display: inline-block; margin-right: 15px">
                      <span style="margin-right: 4px">
                        {{ $t("search.copy") }}
                      </span>
                      <a-icon type="copy" />
                    </div>
                  </div>
                  <pre
                    style="
                      word-break: break-word;
                      white-space: pre-wrap;
                      width: 100%;
                    "
                    >{{ detailData.banner.trim() }}</pre
                  >
                </main>
              </a-tab-pane>
              <a-tab-pane
                key="2"
                tab="应用组件信息"
                v-if="!_isEmpty(detailData.info)"
              >
                <div class="bannerContainer">
                  <a-table
                    :columns="columns"
                    :data-source="componentList"
                    :pagination="false"
                  >
                    <span slot="component" slot-scope="text">
                      <span class="component">{{ text }}</span>
                    </span>
                    <span slot="tag" slot-scope="record">
                      <span>{{ record.join(",") }}</span>
                    </span>
                    <span slot="bug" slot-scope="record">
                      <span v-if="record.length === 0">-</span>
                      <div
                        v-else
                        v-for="(item, index) in record"
                        :key="index"
                        style="margin-bottom: 8px"
                      >
                        {{ item.cve_id }}
                        <a-tag color="#f50">
                          {{ item.level }}
                        </a-tag>
                        <br />
                      </div>
                    </span>
                  </a-table>
                </div>
              </a-tab-pane>
              <a-tab-pane
                key="3"
                tab="证书信息"
                v-if="!_isEmpty(detailData.cert)"
              >
                <main class="bannerContainer" style="height: calc(100% - 40px)">
                  <div v-show="certTpye === '基础信息'">
                    <header class="CATitle">证书信息</header>
                    <div class="CAItem">
                      <span class="itemKey">版本:</span>
                      <span class="itemValue"
                        >V{{ detailData.cert.version }}</span
                      >
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">有效期:</span>
                      <span class="itemValue">剩余{{ remainDay }}天</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">序列号:</span>
                      <span class="itemValue">{{
                        detailData.cert.serial_number
                      }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">证书链长度:</span>
                      <span class="itemValue">{{
                        (detailData.cert && detailData.cert.chain_count) || "-"
                      }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">是否自签名:</span>
                      <span class="itemValue">{{
                        (detailData.cert && detailData.cert.is_self_signed) ||
                        "-"
                      }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">服务器公钥信息:</span>
                      <span class="itemValue">{{
                        (detailData.cert && detailData.cert.pub_key) || "-"
                      }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">SHA-1:</span>
                      <span class="itemValue">{{
                        detailData.cert.sha1 || "-"
                      }}</span>
                    </div>
                    <header class="CATitle">使用者信息</header>
                    <div class="CAItem">
                      <span class="itemKey">使用者(CN):</span>
                      <span class="itemValue">{{ subject["CN"] }}</span>
                    </div>
                    <div
                      class="CAItem"
                      v-show="
                        _get(detailData, 'cert.extension', false) &&
                        Object.keys(detailData.cert.extension).length !== 0
                      "
                    >
                      <span class="itemKey">使用者可选名称(SAN):</span>
                      <span class="itemValue">{{
                        _get(detailData, "cert.extension.dns", []).join(",") ||
                        "-"
                      }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">备案名称(O):</span>
                      <span class="itemValue">{{ subject["O"] }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">地区:</span>
                      <span class="itemValue">
                        <span
                          :class="'fi fi-' + subject['C'].toLowerCase()"
                        ></span>
                        {{ subject["C"] }}
                      </span>
                    </div>
                    <header class="CATitle">颁发者信息</header>
                    <div class="CAItem">
                      <span class="itemKey">使用者(CN):</span>
                      <span class="itemValue">{{ issuer["CN"] }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">颁发机构(O):</span>
                      <span class="itemValue">{{ issuer["O"] }}</span>
                    </div>
                    <div class="CAItem">
                      <span class="itemKey">地区:</span>
                      <span class="itemValue">
                        <span
                          :class="'fi fi-' + issuer['C'].toLowerCase()"
                        ></span>
                        {{ issuer["C"] }}
                      </span>
                    </div>
                  </div>
                  <!-- <div v-show="certTpye === '原始信息'">
                        <div
                          style="
                            text-align: right;
                            color: #396fff;
                            cursor: pointer;
                          "
                        >
                          <span style="margin-right: 4px">{{
                            $t("search.copy")
                          }}</span>
                          <a-icon
                            type="copy"
                            class="cursor-pointer"
                            v-clipboard:copy="detailData.cert.raw"
                            v-clipboard:success="onCopySuccess"
                            v-clipboard:error="onCopyError"
                          />
                        </div>
                        <pre
                          style="
                            word-break: break-word;
                            white-space: pre-wrap;
                            width: 100%;
                            overflow-y: auto;
                          "
                          >{{ detailData.cert.raw.trim() }}</pre
                        >
                      </div> -->
                </main>
              </a-tab-pane>
              <a-tab-pane key="4" tab="tls" v-if="!_isEmpty(detailData.ssl)">
                <main class="bannerContainer">
                  <p>
                    <span class="itemKey">version: </span>
                    <span class="itemValue">{{
                      detailData.ssl.version || "-"
                    }}</span>
                  </p>
                  <p>
                    <span class="itemKey">jarm: </span>
                    <span class="itemValue">{{
                      detailData.ssl.jarm || "-"
                    }}</span>
                  </p>
                  <p>
                    <span class="itemKey">ja3s: </span>
                    <span class="itemValue">{{
                      detailData.ssl.ja3s || "-"
                    }}</span>
                  </p>
                </main>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
      <a-result v-show="!getResult" status="error" title="出错了！"> </a-result>
    </div>
  </div>
</template>
<script>
import { getIPDetail } from "@/api/search";
import { ip_detail_status } from "@/util/memoryUtils";
import { onCopySuccess, onCopyError } from "@/util/tool";
import { testSubject } from "@/util/regx";
let ports = [];
export default {
  name: "RightDetails",
  props: ["ip", "port", "transport", "domain"],
  data() {
    return {
      detailIsLoading: false, // IP详情是否正在加载中
      getResult: true, //结果成功返回
      detaildata: {}, // IP详情响应数据
      detailData: {},
      remainDay: "-", // 证书有效期
      subject: {
        C: "-",
        CN: "-",
        ST: "-",
      }, // 使用者信息
      issuer: {
        C: "-",
        CN: "-",
        ST: "-",
      }, // 颁发者信息
      certTpye: "基础信息",
      componentList: [],
      columns: [
        {
          title: "组件",
          dataIndex: "component",
          width: 170,
          scopedSlots: { customRender: "component" },
        },
        {
          title: "标签",
          dataIndex: "tag",
          scopedSlots: { customRender: "tag" },
        },
        {
          title: "漏洞",
          dataIndex: "bug",
          scopedSlots: { customRender: "bug" },
        },
      ],
    };
  },
  methods: {
    onCopySuccess,
    onCopyError,
    // 获取IP资产详情数据
    async getIPDetailData(q, port, transport, domain) {
      this.detailIsLoading = true;
      // this.getResult = false;
      let localIpDetail = ip_detail_status.getIpDetail(this.port);
      if (localIpDetail) {
        this.detailData = localIpDetail;
        this.handleIPDetails();
        this.detailIsLoading = false;
        this.getResult = true;
      } else {
        try {
          let res = await getIPDetail(q, {
            port: port,
            transport: transport,
            domain: domain,
          });
          res.data.data.code = res.data.code;
          this.detailData = res.data.data;
          this.handleIPDetails();
          ports.push(port);
          ip_detail_status.saveIpDetail(port, this.detailData);
          this.getResult = true;
          this.detailIsLoading = false;
        } catch (err) {
          console.log(err);
          this.detailIsLoading = false;
          this.getResult = false;
        }
      }
    },
    handleIPDetails() {
      if (this.detailData.code == 10005) {
        this.$message.info(`transport只能为TCP,UDP,tcp,udp 或空`, 3);
      } else if (this.detailData.code == 0) {
        this.componentList = [];
        if (Object.keys(this.detailData.cert).length !== 0) {
          // 获取证书有效期
          if (
            this.detailData.cert.not_before &&
            this.detailData.cert.not_after &&
            this.detailData.cert.not_before !== "" &&
            this.detailData.cert.not_after !== ""
          ) {
            let start = this.detailData.cert.not_before.slice(0, 10);
            let end = this.detailData.cert.not_after.slice(0, 10);
            let strSeparator = "-";
            let oDate1 = start.split(strSeparator);
            let oDate2 = end.split(strSeparator);
            let strDateS = new Date(oDate1[0], oDate1[1] - 1, oDate1[2]);
            let strDateE = new Date(oDate2[0], oDate2[1] - 1, oDate2[2]);
            //把相差的毫秒数转换为天数
            this.remainDay = parseInt(
              Math.abs(strDateS - strDateE) / 1000 / 60 / 60 / 24
            );
          }
          if (this.detailData.cert.subject) {
            let tempSubject = testSubject(this.detailData.cert.subject);
            // 获取证书使用者信息
            tempSubject.forEach((item) => {
              let temp = item.split("=");
              this.subject[temp[0]] = temp[1];
            });
          } else {
            this.subject["O"] = "-";
            this.subject["CN"] = "-";
          }
          if (this.detailData.cert.issuer) {
            let tempIssuer = testSubject(this.detailData.cert.issuer);
            // 获取证书颁布者信息
            tempIssuer.forEach((item) => {
              let temp = item.split("=");
              this.issuer[temp[0]] = temp[1];
            });
          } else {
            this.subject["O"] = "-";
            this.subject["CN"] = "-";
          }
        } else {
          this.remainDay = "-";
          this.subject["C"] = "-";
          this.subject["O"] = "-";
          this.subject["ST"] = "-";
          this.issuer["C"] = "-";
          this.issuer["O"] = "-";
          this.issuer["ST"] = "-";
        }
        // 获取厂商信息
        this.detailData.info.forEach((item) => {
          let obj = {};
          obj["component"] = item.rule;
          obj["tag"] = item.tag;
          obj["bug"] = item.vulnerability;
          this.componentList.push(obj);
        });
      }
    },
  },
  mounted() {
    this.getIPDetailData(this.ip, this.port, this.transport, this.domain);
  },
  watch: {
    port() {
      this.getIPDetailData(this.ip, this.port, this.transport, this.domain);
    }
  },
  destroyed() {
    ip_detail_status.removeIpDetail();
  },
};
</script>
<style scoped lang="scss">
.rightDetails {
  flex-basis: 1400px;
  flex-grow: 1;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: white;
  .IPDetails {
    height: 100%;
    overflow: auto;
    padding: 0 28px;

    .IPTitle {
      height: 40px;
      border-bottom: 2px solid #eee;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333333;

      .link {
        color: rgb(111, 186, 44);
        margin: 0 8px;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .bannerCard {
      height: calc(100% - 60px);
      display: flex;
      overflow-y: hidden;
      flex-direction: column;

      .IPBannerTop {
        flex-basis: 56px;
        flex-grow: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 16px 0;
        line-height: 24px;

        .webTitle {
          font-size: 16px;
          font-weight: bold;
        }

        .lastSeen {
          display: flex;
          align-items: center;
          margin-right: 48px;
          color: #595959;
          font-size: 12px;
        }
      }

      .labelList {
        flex-basis: 24px;
        flex-grow: 0;
        line-height: 24px;
        margin-bottom: 16px;

        .IPStatus {
          margin-right: 8px;

          &::before {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
            display: inline-block;
            content: " ";
          }
        }

        .label {
          display: inline-flex;
          align-items: center;
          background-color: rgba(165, 212, 173, 0.2);
          border-radius: 16px;
          padding: 0 12px;
          line-height: 20px;
          margin-right: 8px;
        }
      }

      .bannerContent {
        width: 100%;
        height: 100%;
        flex-grow: 1;
        overflow-y: hidden;
        display: flex;
        flex-direction: column;

        .component {
          display: inline-flex;
          align-items: center;
          padding: 0 8px;
          line-height: 20px;
          background: #edeef3;
          border-radius: 10px;
        }

        .bannerContainer {
          width: 100%;
          height: 600px;
          overflow-y: auto;
          position: relative;
          border: 1px solid #edeef3;
          padding: 12px 16px;

          .itemKey {
            color: rgb(87, 87, 87);
          }

          .itemValue {
            color: rgb(37, 37, 37);
            margin-left: 8px;
          }

          .CATitle {
            font-weight: bold;
            line-height: 16px;
            font-size: 16px;
            padding: 16px 0;

            &::before {
              display: inline-block;
              content: "";
              height: 12px;
              width: 2px;
              background-color: rgb(111, 186, 44);
              margin-right: 4px;
            }
          }

          .CAItem {
            line-height: 24px;
          }
        }
      }
    }
  }
}
.loadingMove {
  width: 100%;
  padding: 40px 76px 0 28px;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
