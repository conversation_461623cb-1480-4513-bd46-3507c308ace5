import { message } from "ant-design-vue";

const routerPermission = (to, from, next) => {
  if (to.meta.loginRequired) {
    let username = localStorage.getItem('token');
    let result = username?'authorised':'loginRequired';

    switch (result) {
      //未登录
      case "loginRequired":
        message.info(
          "未登录用户不能进行查询，请登录使用。"
        );
        next({ path: "/login", query: { path: to.path, query: to.query } });
        break;
      case "authorised":
        next();
        break;
      default:
        next({ path: from.fullPath });
    }
  } else {
    next();
  }
};
export default routerPermission;
