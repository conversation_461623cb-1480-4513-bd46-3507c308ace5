<template>
  <div class="worldMap" id="world">
    <v-chart :option="option" v-if="ready" :autoresize="true"/>
  </div>
</template>

<script>
import Vue from "vue";
import VChart, {THEME_KEY} from "vue-echarts";
import ECharts from "vue-echarts";
import { CanvasRenderer } from "echarts/renderers";
import { <PERSON><PERSON><PERSON>, TreeChart } from "echarts/charts";
import {
  GeoComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from "echarts/components";
import { use } from "echarts/core";
import {registerMap} from "echarts/core";
import world from "@/assets/json/world.json";

Vue.component("v-chart", ECharts);
use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
  Geo<PERSON>omponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
]);

export default {
  name: "WorldMap",
  components: {
    <PERSON><PERSON>
  },
  provide: {
    [THEME_KEY]: "light"
  },
  props: {
    worldMapList: Array,
  },
  data() {
    return {
      ready: false,
      option: {
        tooltip: {
          show: true,
          trigger: 'item',
          triggerOn: 'mousemove',
          position: 'top',
        },
        toolbox: {
          show: true,
          feature: {
            myFull: {
              show: true,
              title: '全屏查看',
              icon: "path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891",
              onclick: () => {
                const element = document.getElementById('world');
                if (element.requestFullScreen) { // HTML W3C 提议
                  element.requestFullScreen();
                } else if (element.msRequestFullscreen) { // IE11
                  element.msRequestFullScreen();
                } else if (element.webkitRequestFullScreen) { // Webkit (works in Safari5.1 and Chrome 15)
                  element.webkitRequestFullScreen();
                } else if (element.mozRequestFullScreen) { // Firefox (works in nightly)
                  element.mozRequestFullScreen();
                }
              },
            }
          }
        },
        visualMap: {
          left: '10',
          bottom: '0',
          type: 'piecewise',
          pieces: [
            {min: 1},
            {min: 0, max: 0},
          ],
          inRange: {
            color: [
              '#f3f3f3',
              '#55a722',
            ]
          }
        },
        series: [
          {
            name: '地区分布',
            type: 'map',
            map: 'world',
            nameMap: {
              'Afghanistan': '阿富汗',
              'Angola': '安哥拉',
              'Albania': '阿尔巴尼亚',
              'United Arab Emirates': '阿联酋',
              'Argentina': '阿根廷',
              'Armenia': '亚美尼亚',
              'Australia': '澳大利亚',
              'Austria': '奥地利',
              'Azerbaijan': '阿塞拜疆',
              'Burundi': '布隆迪',
              'Belgium': '比利时',
              'Benin': '贝宁',
              'Burkina Faso': '布基纳法索',
              'Bangladesh': '孟加拉',
              'Bulgaria': '保加利亚',
              'Belarus': '白俄罗斯',
              'Belize': '伯利兹',
              'Bermuda': '百慕大',
              'Bolivia': '玻利维亚',
              'Brazil': '巴西',
              'Brunei': '文莱',
              'Bhutan': '不丹',
              'Botswana': '博茨瓦纳',
              'Canada': '加拿大',
              'Switzerland': '瑞士',
              'Chile': '智利',
              'China': '中国',
              'Cameroon': '喀麦隆',
              'Colombia': '哥伦比亚',
              'Costa Rica': '哥斯达黎加',
              'Cuba': '古巴',
              'Cyprus': '塞浦路斯',
              'Germany': '德国',
              'Djibouti': '吉布提',
              'Denmark': '丹麦',
              'Algeria': '阿尔及利亚',
              'Ecuador': '厄瓜多尔',
              'Egypt': '埃及',
              'Eritrea': '厄立特里亚',
              'Spain': '西班牙',
              'Estonia': '爱沙尼亚',
              'Ethiopia': '埃塞俄比亚',
              'Finland': '芬兰',
              'Fiji': '斐济',
              'France': '法国',
              'Gabon': '加蓬',
              'United Kingdom': '英国',
              'Georgia': '格鲁吉亚',
              'Ghana': '加纳',
              'Guinea': '几内亚',
              'Gambia': '冈比亚',
              'Greece': '希腊',
              'Greenland': '格陵兰',
              'Guatemala': '危地马拉',
              'Guyana': '圭亚那',
              'Honduras': '洪都拉斯',
              'Croatia': '克罗地亚',
              'Haiti': '海地',
              'Hungary': '匈牙利',
              'Indonesia': '印度尼西亚',
              'India': '印度',
              'Ireland': '爱尔兰',
              'Iran': '伊朗',
              'Iraq': '伊拉克',
              'Iceland': '冰岛',
              'Israel': '以色列',
              'Italy': '意大利',
              'Jamaica': '牙买加',
              'Jordan': '约旦',
              'Japan': '日本',
              'Kazakhstan': '哈萨克斯坦',
              'Kenya': '肯尼亚',
              'Kyrgyzstan': '吉尔吉斯斯坦',
              'Cambodia': '柬埔寨',
              'Korea': '韩国',
              'Kuwait': '科威特',
              'Lebanon': '黎巴嫩',
              'Liberia': '利比里亚',
              'Libya': '利比亚',
              'Sri Lanka': '斯里兰卡',
              'Lesotho': '莱索托',
              'Lithuania': '立陶宛',
              'Luxembourg': '卢森堡',
              'Latvia': '拉脱维亚',
              'Morocco': '摩洛哥',
              'Moldova': '摩尔多瓦',
              'Madagascar': '马达加斯加',
              'Mexico': '墨西哥',
              'Macedonia': '马其顿',
              'Mali': '马里',
              'Myanmar': '缅甸',
              'Montenegro': '黑山',
              'Mongolia': '蒙古',
              'Mozambique': '莫桑比克',
              'Mauritania': '毛里塔尼亚',
              'Malawi': '马拉维',
              'Malaysia': '马来西亚',
              'Namibia': '纳米比亚',
              'New Caledonia': '新喀里多尼亚',
              'Niger': '尼日尔',
              'Nigeria': '尼日利亚',
              'Nicaragua': '尼加拉瓜',
              'Netherlands': '荷兰',
              'Norway': '挪威',
              'Nepal': '尼泊尔',
              'New Zealand': '新西兰',
              'Oman': '阿曼',
              'Pakistan': '巴基斯坦',
              'Panama': '巴拿马',
              'Peru': '秘鲁',
              'Philippines': '菲律宾',
              'Papua New Guinea': '巴布亚新几内亚',
              'Poland': '波兰',
              'Puerto Rico': '波多黎各',
              'Portugal': '葡萄牙',
              'Paraguay': '巴拉圭',
              'Qatar': '卡塔尔',
              'Romania': '罗马尼亚',
              'Russia': '俄罗斯',
              'Rwanda': '卢旺达',
              'Saudi Arabia': '沙特阿拉伯',
              'Sudan': '苏丹',
              'Senegal': '塞内加尔',
              'Sierra Leone': '塞拉利昂',
              'El Salvador': '萨尔瓦多',
              'Somalia': '索马里',
              'Suriname': '苏里南',
              'Slovakia': '斯洛伐克',
              'Slovenia': '斯洛文尼亚',
              'Sweden': '瑞典',
              'Swaziland': '斯威士兰',
              'Syria': '叙利亚',
              'Chad': '乍得',
              'Togo': '多哥',
              'Thailand': '泰国',
              'Tajikistan': '塔吉克斯坦',
              'Turkmenistan': '土库曼斯坦',
              'Trinidad and Tobago': '特立尼达和多巴哥',
              'Tunisia': '突尼斯',
              'Turkey': '土耳其',
              'Uganda': '乌干达',
              'Ukraine': '乌克兰',
              'Uruguay': '乌拉圭',
              'United States': '美国',
              'Uzbekistan': '乌兹别克斯坦',
              'Venezuela': '委内瑞拉',
              'Vietnam': '越南',
              'Vanuatu': '瓦努阿图',
              'Yemen': '也门',
              'South Africa': '南非',
              'Zambia': '赞比亚',
              'Zimbabwe': '津巴布韦',
              'Liechtenstein': '列支敦士登',
              'Serbia': '塞尔维亚',
              "Aland": "奥兰群岛",
              "Andorra": "安道尔",
              "American Samoa": "美属萨摩亚",
              "Antigua and Barb.": "安提瓜和巴布达",
              "Bahrain": "巴林",
              "Bahamas": "巴哈马",
              "Bosnia and Herz.": "波斯尼亚和黑塞哥维那",
              "Barbados": "巴巴多斯",
              "Central African Rep.": "中非",
              "Dem. Rep. Congo": "刚果民主共和国",
              "Congo": "刚果",
              "Comoros": "科摩罗",
              "Cape Verde": "佛得角",
              "Curaçao": "库拉索",
              "Cayman Is.": "开曼群岛",
              "Czech Rep.": "捷克",
              "Dominica": "多米尼克",
              "Falkland Is.": "福克兰群岛",
              "Faeroe Is.": "法罗群岛",
              "Micronesia": "密克罗尼西亚联邦",
              "Guinea-Bissau": "几内亚比绍",
              "Eq. Guinea": "赤道几内亚",
              "Grenada": "格林纳达",
              "Guam": "关岛",
              "Isle of Man": "马恩岛",
              "Br. Indian Ocean Ter.": "英属印度洋领地",
              "Jersey": "泽西岛",
              "Kiribati": "基里巴斯",
              "Lao PDR": "老挝",
              "Saint Lucia": "圣卢西亚",
              "Malta": "马耳他",
              "N. Mariana Is.": "北马里亚纳群岛",
              "Montserrat": "蒙特塞拉特岛",
              "Mauritius": "毛里求斯",
              "Niue": "纽埃岛",
              "Palau": "帕劳",
              "Dem. Rep. Korea": "韩国",
              "Palestine": "巴勒斯坦",
              "Fr. Polynesia": "法属波利尼西亚",
              "S. Sudan": "南苏丹",
              "Singapore": "新加坡",
              "Saint Helena": "圣赫勒拿",
              "Solomon Is.": "所罗门群岛",
              "St. Pierre and Miquelon": "圣皮埃尔和密克隆群岛",
              "São Tomé and Principe": "圣多美和普林西比",
              "Seychelles": "塞舌尔",
              "Turks and Caicos Is.": "特克斯和凯科斯群岛",
              "Timor-Leste": "东帝汶",
              "Tonga": "汤加",
              "Tanzania": "坦桑尼亚",
              "St. Vin. and Gren.": "圣文森特和格林纳丁斯",
              "U.S. Virgin Is.": "美属维尔京群岛",
              "Samoa": "萨摩亚",
              "W. Sahara": "西撒哈拉",
              "Fr. S. Antarctic Lands": "马提尼克岛",
              "Côte d'Ivoire": "科特迪瓦",
              "N. Cyprus": "东塞浦路斯",
              "Dominican Rep.": "多米尼加",
              "Heard I. and McDonald Is.": "赫德岛和麦克唐纳群岛",
              "Siachen Glacier": "锡亚琴冰川",
              "S. Geo. and S. Sandw. Is.": "南乔治亚岛与南桑威奇群岛"
            },
            emphasis: {
              label: {
                show: false
              }
            },
            data: [
              {
                name: '美国',
                value: 1832749
              },
              {
                name: '中国',
                value: 123234
              },
            ],
          }
        ]
      },
    }
  },
  methods: {
    // 获取某个数的整零
    getWhole(num) {
      return (num - num % Math.pow(10, String(num).length - 1));
    }
  },
  mounted() {
    registerMap('world', {geoJSON: world});
    let allData = [
      {
        name: '阿富汗',
        value: 0
      }, {
        name: '安哥拉',
        value: 0
      }, {
        name: '阿尔巴尼亚',
        value: 0
      }, {
        name: '阿尔巴尼亚',
        value: 0
      }, {
        name: '阿根廷',
        value: 0
      }, {
        name: '亚美尼亚',
        value: 0
      }, {
        name: '澳大利亚',
        value: 0
      }, {
        name: '奥地利',
        value: 0
      }, {
        name: '阿塞拜疆',
        value: 0
      }, {
        name: '布隆迪',
        value: 0
      }, {
        name: '比利时',
        value: 0
      }, {
        name: '贝宁',
        value: 0
      }, {
        name: '布基纳法索',
        value: 0
      }, {
        name: '孟加拉',
        value: 0
      }, {
        name: '保加利亚',
        value: 0
      }, {
        name: '白俄罗斯',
        value: 0
      }, {
        name: '伯利兹',
        value: 0
      }, {
        name: '百慕大',
        value: 0
      }, {
        name: '玻利维亚',
        value: 0
      }, {
        name: '巴西',
        value: 0
      }, {
        name: '文莱',
        value: 0
      }, {
        name: '不丹',
        value: 0
      }, {
        name: '博茨瓦纳',
        value: 0
      }, {
        name: '加拿大',
        value: 0
      }, {
        name: '瑞士',
        value: 0
      }, {
        name: '智利',
        value: 0
      }, {
        name: '中国',
        value: 0
      }, {
        name: '喀麦隆',
        value: 0
      }, {
        name: '哥伦比亚',
        value: 0
      }, {
        name: '哥斯达黎加',
        value: 0
      }, {
        name: '古巴',
        value: 0
      }, {
        name: '塞浦路斯',
        value: 0
      }, {
        name: '德国',
        value: 0
      }, {
        name: '吉布提',
        value: 0
      }, {
        name: '丹麦',
        value: 0
      }, {
        name: '阿尔及利亚',
        value: 0
      }, {
        name: '厄瓜多尔',
        value: 0
      }, {
        name: '埃及',
        value: 0
      }, {
        name: '厄立特里亚',
        value: 0
      }, {
        name: '西班牙',
        value: 0
      }, {
        name: '爱沙尼亚',
        value: 0
      }, {
        name: '埃塞俄比亚',
        value: 0
      }, {
        name: '芬兰',
        value: 0
      }, {
        name: '斐济',
        value: 0
      }, {
        name: '法国',
        value: 0
      }, {
        name: '加蓬',
        value: 0
      }, {
        name: '英国',
        value: 0
      }, {
        name: '格鲁吉亚',
        value: 0
      }, {
        name: '加纳',
        value: 0
      }, {
        name: '几内亚',
        value: 0
      }, {
        name: '冈比亚',
        value: 0
      }, {
        name: '希腊',
        value: 0
      }, {
        name: '格陵兰',
        value: 0
      }, {
        name: '危地马拉',
        value: 0
      }, {
        name: '圭亚那',
        value: 0
      }, {
        name: '洪都拉斯',
        value: 0
      }, {
        name: '克罗地亚',
        value: 0
      }, {
        name: '海地',
        value: 0
      }, {
        name: '匈牙利',
        value: 0
      }, {
        name: '印度尼西亚',
        value: 0
      }, {
        name: '印度',
        value: 0
      }, {
        name: '爱尔兰',
        value: 0
      }, {
        name: '伊朗',
        value: 0
      }, {
        name: '伊拉克',
        value: 0
      }, {
        name: '冰岛',
        value: 0
      }, {
        name: '以色列',
        value: 0
      }, {
        name: '意大利',
        value: 0
      }, {
        name: '牙买加',
        value: 0
      }, {
        name: '约旦',
        value: 0
      }, {
        name: '日本',
        value: 0
      }, {
        name: '哈萨克斯坦',
        value: 0
      }, {
        name: '肯尼亚',
        value: 0
      }, {
        name: '吉尔吉斯斯坦',
        value: 0
      }, {
        name: '柬埔寨',
        value: 0
      }, {
        name: '韩国',
        value: 0
      }, {
        name: '科威特',
        value: 0
      }, {
        name: '黎巴嫩',
        value: 0
      }, {
        name: '利比里亚',
        value: 0
      }, {
        name: '利比亚',
        value: 0
      }, {
        name: '斯里兰卡',
        value: 0
      }, {
        name: '莱索托',
        value: 0
      }, {
        name: '立陶宛',
        value: 0
      }, {
        name: '卢森堡',
        value: 0
      }, {
        name: '拉脱维亚',
        value: 0
      }, {
        name: '摩洛哥',
        value: 0
      }, {
        name: '摩尔多瓦',
        value: 0
      }, {
        name: '马达加斯加',
        value: 0
      }, {
        name: '墨西哥',
        value: 0
      }, {
        name: '马其顿',
        value: 0
      }, {
        name: '马里',
        value: 0
      }, {
        name: '缅甸',
        value: 0
      }, {
        name: '黑山',
        value: 0
      }, {
        name: '蒙古',
        value: 0
      }, {
        name: '莫桑比克',
        value: 0
      }, {
        name: '毛里塔尼亚',
        value: 0
      }, {
        name: '马拉维',
        value: 0
      }, {
        name: '马来西亚',
        value: 0
      }, {
        name: '纳米比亚',
        value: 0
      }, {
        name: '新喀里多尼亚',
        value: 0
      }, {
        name: '尼日尔',
        value: 0
      }, {
        name: '尼日利亚',
        value: 0
      }, {
        name: '尼加拉瓜',
        value: 0
      }, {
        name: '荷兰',
        value: 0
      }, {
        name: '挪威',
        value: 0
      }, {
        name: '尼泊尔',
        value: 0
      }, {
        name: '新西兰',
        value: 0
      }, {
        name: '阿曼',
        value: 0
      }, {
        name: '巴基斯坦',
        value: 0
      }, {
        name: '巴拿马',
        value: 0
      }, {
        name: '秘鲁',
        value: 0
      }, {
        name: '菲律宾',
        value: 0
      }, {
        name: '巴布亚新几内亚',
        value: 0
      }, {
        name: '波兰',
        value: 0
      }, {
        name: '波多黎各',
        value: 0
      }, {
        name: '葡萄牙',
        value: 0
      }, {
        name: '巴拉圭',
        value: 0
      }, {
        name: '卡塔尔',
        value: 0
      }, {
        name: '罗马尼亚',
        value: 0
      }, {
        name: '俄罗斯',
        value: 0
      }, {
        name: '卢旺达',
        value: 0
      }, {
        name: '沙特阿拉伯',
        value: 0
      }, {
        name: '苏丹',
        value: 0
      }, {
        name: '塞内加尔',
        value: 0
      }, {
        name: '塞拉利昂',
        value: 0
      }, {
        name: '萨尔瓦多',
        value: 0
      }, {
        name: '索马里',
        value: 0
      }, {
        name: '苏里南',
        value: 0
      }, {
        name: '斯洛伐克',
        value: 0
      }, {
        name: '斯洛文尼亚',
        value: 0
      }, {
        name: '瑞典',
        value: 0
      }, {
        name: '斯威士兰',
        value: 0
      }, {
        name: '叙利亚',
        value: 0
      }, {
        name: '乍得',
        value: 0
      }, {
        name: '多哥',
        value: 0
      }, {
        name: '泰国',
        value: 0
      }, {
        name: '塔吉克斯坦',
        value: 0
      }, {
        name: '土库曼斯坦',
        value: 0
      }, {
        name: '特立尼达和多巴哥',
        value: 0
      }, {
        name: '突尼斯',
        value: 0
      }, {
        name: '土耳其',
        value: 0
      }, {
        name: '乌干达',
        value: 0
      }, {
        name: '乌克兰',
        value: 0
      }, {
        name: '乌拉圭',
        value: 0
      }, {
        name: '美国',
        value: 0
      }, {
        name: '乌兹别克斯坦',
        value: 0
      }, {
        name: '委内瑞拉',
        value: 0
      }, {
        name: '越南',
        value: 0
      }, {
        name: '瓦努阿图',
        value: 0
      }, {
        name: '也门',
        value: 0
      }, {
        name: '南非',
        value: 0
      }, {
        name: '赞比亚',
        value: 0
      }, {
        name: '津巴布韦',
        value: 0
      }, {
        name: '列支敦士登',
        value: 0
      }, {
        name: '塞尔维亚',
        value: 0
      }, {
        name: '奥兰群岛',
        value: 0
      }, {
        name: '安道尔',
        value: 0
      }, {
        name: '美属萨摩亚',
        value: 0
      }, {
        name: '安提瓜和巴布达',
        value: 0
      }, {
        name: '巴林',
        value: 0
      }, {
        name: '巴哈马',
        value: 0
      }, {
        name: '波斯尼亚和黑塞哥维那',
        value: 0
      }, {
        name: '巴巴多斯',
        value: 0
      }, {
        name: '中非',
        value: 0
      }, {
        name: '刚果民主共和国',
        value: 0
      }, {
        name: '刚果',
        value: 0
      }, {
        name: '科摩罗',
        value: 0
      }, {
        name: '佛得角',
        value: 0
      }, {
        name: '库拉索',
        value: 0
      }, {
        name: '开曼群岛',
        value: 0
      }, {
        name: '捷克',
        value: 0
      }, {
        name: '多米尼克',
        value: 0
      }, {
        name: '福克兰群岛',
        value: 0
      }, {
        name: '法罗群岛',
        value: 0
      }, {
        name: '密克罗尼西亚联邦',
        value: 0
      }, {
        name: '几内亚比绍',
        value: 0
      }, {
        name: '赤道几内亚',
        value: 0
      }, {
        name: '格林纳达',
        value: 0
      }, {
        name: '关岛',
        value: 0
      }, {
        name: '马恩岛',
        value: 0
      }, {
        name: '英属印度洋领地',
        value: 0
      }, {
        name: '泽西岛',
        value: 0
      }, {
        name: '基里巴斯',
        value: 0
      }, {
        name: '老挝',
        value: 0
      }, {
        name: '圣卢西亚',
        value: 0
      }, {
        name: '马耳他',
        value: 0
      }, {
        name: '北马里亚纳群岛',
        value: 0
      }, {
        name: '蒙特塞拉特岛',
        value: 0
      }, {
        name: '毛里求斯',
        value: 0
      }, {
        name: '纽埃岛',
        value: 0
      }, {
        name: '帕劳',
        value: 0
      }, {
        name: '韩国',
        value: 0
      }, {
        name: '巴勒斯坦',
        value: 0
      }, {
        name: '法属波利尼西亚',
        value: 0
      }, {
        name: '南苏丹',
        value: 0
      }, {
        name: '新加坡',
        value: 0
      }, {
        name: '圣赫勒拿',
        value: 0
      }, {
        name: '所罗门群岛',
        value: 0
      }, {
        name: '圣皮埃尔和密克隆群岛',
        value: 0
      }, {
        name: '圣多美和普林西比',
        value: 0
      }, {
        name: '塞舌尔',
        value: 0
      }, {
        name: '特克斯和凯科斯群岛',
        value: 0
      }, {
        name: '东帝汶',
        value: 0
      }, {
        name: '汤加',
        value: 0
      }, {
        name: '坦桑尼亚',
        value: 0
      }, {
        name: '圣文森特和格林纳丁斯',
        value: 0
      }, {
        name: '美属维尔京群',
        value: 0
      }, {
        name: '萨摩亚',
        value: 0
      }, {
        name: '西撒哈拉',
        value: 0
      }, {
        name: '马提尼克岛',
        value: 0
      }, {
        name: '科特迪瓦',
        value: 0
      }, {
        name: '东塞浦路斯',
        value: 0
      }, {
        name: '多米尼加',
        value: 0
      }, {
        name: '赫德岛和麦克唐纳群岛',
        value: 0
      }, {
        name: '锡亚琴冰川',
        value: 0
      }, {
        name: '南乔治亚岛与南桑威奇群岛',
        value: 0
      }
    ]
    if (this.worldMapList.length > 0) {
      // 找出分布最小值和3/4值和最大值
      let worldData = []
      let leastNum = 1000000;
      let maxNum = 0;
      this.worldMapList.forEach((item) => {
        worldData.push(item.count)
        maxNum = item.count > maxNum ? item.count : maxNum;
        leastNum = item.count < leastNum ? item.count : leastNum;
      })
      worldData.sort(function (a, b) {
        return a - b
      });
      if (worldData.length < 4) {
        this.option.visualMap.pieces = [];
        this.option.visualMap.pieces.push({
          min: leastNum,
          max: maxNum
        })
        this.option.visualMap.pieces.push({
          max: this.getWhole(leastNum)
        })
      } else {
        let quarter = worldData[parseInt(worldData.length / 4) * 3]
        this.option.visualMap.pieces = [];
        this.option.visualMap.pieces.push({
          min: this.getWhole(maxNum)
        });
        this.option.visualMap.pieces.push({
          min: this.getWhole(quarter),
          max: this.getWhole(maxNum)
        });
        this.option.visualMap.pieces.push({
          min: this.getWhole(leastNum),
          max: this.getWhole(quarter)
        });
        this.option.visualMap.pieces.push({
          max: this.getWhole(leastNum)
        });
      }
    }
    // 遍历世界数据
    for (let i = 0; i < this.worldMapList.length; i++) {
      allData.forEach((item, index, arr) => {
        if (item.name === this.worldMapList[i].zhcn_name) {
          arr[index].value = this.worldMapList[i].count;
        }
      })
    }
    this.option.series[0].data = allData;
    this.ready = true;    // 当options准备好以后再渲染canvas
  },
}
</script>

<style scoped lang="scss">
.worldMap {
  height: 300px;
  width: 100%;
  background-color: #fff;
}
</style>