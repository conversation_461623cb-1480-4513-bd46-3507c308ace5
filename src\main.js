import Vue from "vue";
import App from "./App.vue";
import router from "./router";

// import ECharts from "vue-echarts";
// import { use } from "echarts/core";

import "@/util/global/globalFn"; //引入全局使用的函数
import "/node_modules/flag-icons/css/flag-icons.min.css"; // 引入国旗图标

// import { CanvasRenderer } from "echarts/renderers";
// import { MapChart, TreeChart } from "echarts/charts";
// import {
//   GeoComponent,
//   TitleComponent,
//   ToolboxComponent,
//   TooltipComponent,
//   VisualMapComponent,
// } from "echarts/components";

import VueClipboard from "vue-clipboard2";

VueClipboard.config.autoSetContainer = true;
Vue.use(VueClipboard);

import i18n from "./i18n";

import "ant-design-vue/dist/antd.css";

// 引入ant主题样式
import "@/assets/css/ns-theme.less";

import {
  AutoComplete,
  Affix,
  Alert,
  BackTop,
  Badge,
  Breadcrumb,
  Button,
  Card,
  Checkbox,
  Col,
  Collapse,
  ConfigProvider,
  DatePicker,
  Drawer,
  Dropdown,
  Descriptions,
  Empty,
  Form,
  FormModel,
  Icon,
  Input,
  Layout,
  Menu,
  message,
  Modal,
  Pagination,
  Popover,
  Radio,
  Result,
  Row,
  Select,
  Skeleton,
  Spin,
  Statistic,
  Switch,
  Table,
  Tabs,
  Tag,
  Tooltip,
  Upload,
  notification,
} from "ant-design-vue";

// use([
//   TitleComponent,
//   ToolboxComponent,
//   TooltipComponent,
//   VisualMapComponent,
//   GeoComponent,
//   MapChart,
//   TreeChart,
//   CanvasRenderer,
// ]);

// register globally (or you can do it locally)
// Vue.component("v-chart", ECharts);

Vue.prototype.$message = message;
Vue.prototype.$notification = notification;
message.config({
  top: `100px`,
  maxCount: 1,
});

[
  AutoComplete,
  Button,
  Menu,
  Breadcrumb,
  Input,
  Icon,
  Spin,
  ConfigProvider,
  Descriptions,
  Empty,
  Table,
  Tag,
  Modal,
  Radio,
  Layout,
  Dropdown,
  Drawer,
  Row,
  Result,
  Col,
  Tooltip,
  Popover,
  Form,
  FormModel,
  Tabs,
  Alert,
  BackTop,
  Select,
  DatePicker,
  Card,
  Pagination,
  Skeleton,
  Switch,
  Collapse,
  Checkbox,
  Badge,
  Affix,
  Statistic,
  Upload,
].map((component) => {
  Vue.use(component);
});

Vue.config.productionTip = false;

export default new Vue({
  router,
  i18n,
  render: (h) => h(App),
}).$mount("#app");
