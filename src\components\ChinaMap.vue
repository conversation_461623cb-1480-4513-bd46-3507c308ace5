<template>
  <div class="chinaMap" id="china">
    <v-chart :option="option" v-if="ready" :autoresize="true"/>

  </div>
</template>

<script>
import Vue from "vue";
import VChart, {THEME_KEY} from "vue-echarts";
import ECharts from "vue-echarts";
import { CanvasRenderer } from "echarts/renderers";
import { Map<PERSON>hart, TreeChart } from "echarts/charts";
import {
  GeoComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from "echarts/components";
import { use } from "echarts/core";
import {registerMap} from "echarts/core";
import china from "@/assets/json/china.json";

Vue.component("v-chart", ECharts);
use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
  <PERSON><PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
]);


export default {
  name: "chinaMap",
  components: {
    <PERSON>hart
  },
  provide: {
    [THEME_KEY]: "light"
  },
  props: {
    chinaMapList: Array,
  },
  data() {
    return {
      ready: false,
      option: {
        tooltip: {
          show: true,
          trigger: 'item',
          triggerOn: 'mousemove',
          position: 'top',
        },
        toolbox: {
          show: true,
          feature: {
            myFull: {
              show: true,
              title: '全屏查看',
              icon: "path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891",
              onclick: () => {
                const element = document.getElementById('china');
                if (element.requestFullScreen) { // HTML W3C 提议
                  element.requestFullScreen();
                } else if (element.msRequestFullscreen) { // IE11
                  element.msRequestFullScreen();
                } else if (element.webkitRequestFullScreen) { // Webkit (works in Safari5.1 and Chrome 15)
                  element.webkitRequestFullScreen();
                } else if (element.mozRequestFullScreen) { // Firefox (works in nightly)
                  element.mozRequestFullScreen();
                }
              },
            }
          }
        },
        visualMap: {
          left: '10',
          bottom: '0',
          type: 'piecewise',
          pieces: [
            {min:1},
            {min: 0,max: 0},
          ],
          inRange: {
            color: [
              '#f3f3f3',
              '#55a722',
            ]
          }
        },
        series: [
          {
            name: '地区分布',
            type: 'map',
            map: 'china',
            emphasis: {
              label: {
                show: false
              }
            },
            data: [],
          }
        ]
      },
      maxNum: 0,      // 省份资源最大值
      leastNum: 10000000,     // 省份资源最小值
    }
  },
  methods: {
    // 获取某个数的整零
    getWhole(num) {
      return (num - num % Math.pow(10, String(num).length - 1));
    }
  },
  mounted() {
    registerMap('china', {geoJSON: china});
    let allData = [
      {
        name: '北京市',
        value: 0
      }, {
        name: '天津市',
        value: 0
      }, {
        name: '上海市',
        value: 0
      }, {
        name: '重庆市',
        value: 0
      }, {
        name: '河北省',
        value: 0
      }, {
        name: '河南省',
        value: 0
      }, {
        name: '云南省',
        value: 0
      }, {
        name: '辽宁省',
        value: 0
      }, {
        name: '黑龙江省',
        value: 0
      }, {
        name: '湖南省',
        value: 0
      }, {
        name: '安徽省',
        value: 0
      }, {
        name: '山东省',
        value: 0
      }, {
        name: '新疆维吾尔自治区',
        value: 0
      }, {
        name: '江苏省',
        value: 0
      }, {
        name: '浙江省',
        value: 0
      }, {
        name: '江西省',
        value: 0
      }, {
        name: '湖北省',
        value: 0
      }, {
        name: '广西壮族自治区',
        value: 0
      }, {
        name: '甘肃省',
        value: 0
      }, {
        name: '山西省',
        value: 0
      }, {
        name: '内蒙古自治区',
        value: 0
      }, {
        name: '陕西省',
        value: 0
      }, {
        name: '吉林省',
        value: 0
      }, {
        name: '福建省',
        value: 0
      }, {
        name: '贵州省',
        value: 0
      }, {
        name: '广东省',
        value: 0
      }, {
        name: '青海省',
        value: 0
      }, {
        name: '西藏自治区',
        value: 0
      }, {
        name: '四川省',
        value: 0
      }, {
        name: '宁夏回族自治区',
        value: 0
      }, {
        name: '海南省',
        value: 0
      }, {
        name: '南海诸岛',
        value: 0
      }, {
        name: '台湾省',
        value: 0
      }, {
        name: '香港特别行政区',
        value: 0
      }, {
        name: '澳门特别行政区',
        value: 0
      }
    ];
    if (this.chinaMapList.length > 0) {
      // 找出分布最小值和3/4值和最大值
      let chinaData = []
      let leastNum = 1000000;
      let maxNum = 0;
      this.chinaMapList.forEach((item) => {
        chinaData.push(item.count)
        maxNum = item.count > maxNum ? item.count : maxNum;
        leastNum = item.count < leastNum ? item.count : leastNum;
      })
      chinaData.sort(function (a, b) {
        return a - b
      });
      if (chinaData.length < 4) {
        this.option.visualMap.pieces = [];
        this.option.visualMap.pieces.push({
          min: leastNum,
          max: maxNum
        })
        this.option.visualMap.pieces.push({
          max: this.getWhole(leastNum)
        })
      } else {
        let quarter = chinaData[parseInt(chinaData.length / 4) * 3]
        this.option.visualMap.pieces = [];
        this.option.visualMap.pieces.push({
          min: this.getWhole(maxNum)
        });
        this.option.visualMap.pieces.push({
          min: this.getWhole(quarter),
          max: this.getWhole(maxNum)
        });
        this.option.visualMap.pieces.push({
          min: this.getWhole(leastNum),
          max: this.getWhole(quarter)
        });
        this.option.visualMap.pieces.push({
          max: this.getWhole(leastNum)
        });
      }
    }
    // 遍历中国省内数据
    for (let i = 0; i < this.chinaMapList.length; i++) {
      allData.forEach((item, index, arr) => {
        if (item.name === this.chinaMapList[i].name) {
          arr[index].value = this.chinaMapList[i].count;
        }
      })
    }
    this.option.series[0].data = allData;
    this.ready = true;    // 当options准备好以后再渲染canvas
  },
};
</script>

<style scoped>
.chinaMap {
  height: 300px;
  width: 100%;
  background-color: #fff;
}
</style>