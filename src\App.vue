<template>
  <div class="app" id="ns-content">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script>
import moment from "moment";
import "moment/locale/zh-cn";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import i18n from "@/i18n";
moment.locale(i18n.locale === "en" ? "en" : "zh-cn");

export default {
  name: 'App',
  components: {
  },
  data() {
    return {
      locale: this.$i18n.locale === "en" ? null : zhCN,
    };
  },
}
</script>

<style lang="scss">
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

html,body {
  margin: 0;
  padding: 0;
  * {
    //修改滚动条样式 兼容firefox
    scrollbar-width: thin;
    scrollbar-color: rgb(135, 146, 166) #eee;
  }
}

/*修改滚动条样式 兼容chrome*/
::-webkit-scrollbar-track {
  background: rgb(226, 228, 234);
  border-radius: 0;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 10px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  cursor: pointer;
  border-radius: 5px;
  background: rgb(135, 146, 166);
  transition: color 0.2s ease;
}

</style>
