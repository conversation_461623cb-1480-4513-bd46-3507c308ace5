export function debounce(func, wait) {
  let timeout;
  return function () {
    let context = this,
      args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      timeout = null;
      func.apply(context, args);
    }, wait);
  };
}

export function onCopySuccess(e) {
  this.$message.success(
    "复制成功！" +
      (e.text && e.text.length < 100
        ? e.text
        : e.text.slice(0, 99) + "......")
  );
}

export function onCopyError(e) {
  this.$message.error("复制失败，请重试！", e);
}