<template>
  <div>
    <div>
      {{data.lodaing}}
    </div>
    <a-skeleton active v-if="data.loading" />
    <a-skeleton active v-if="data.loading" />
    <div v-else>
      <div v-if="!data.owner" class="empty_container">
        <a-empty />
      </div>
      <div v-else>
        <div class="recommend_container">
          <a-icon
            type="like"
            theme="twoTone"
            two-tone-color="red"
            style="font-size: 26px; margin-right: 10px"
          />
          <span style="color: gray"> 推荐值： </span>
          {{ data.owner }}
        </div>
        <div class="info_container">
          <div style="font-size: 16px">
            <a-tag v-if="data.usage && data.usage !== ''" color="blue">
              {{ data.usage }}
            </a-tag>
            <span v-if="data.usage && data.location && data.usage !== '' && data.location !== ''">
              -&nbsp;
            </span>
            <!-- TODO：改成country_code -->
            <span v-if="data.location && data.location !== ''" :class="'fi fi-cn'"></span>
            {{ data.location }}
          </div>
        </div>

        <header class="subTitle">
          <span>证据链</span>
        </header>
        <div style="width: 100%; margin: auto">
          <a-row class="table">
            <a-col :span="4" class="table_title"> 依据点 </a-col>
            <a-col :span="8" class="table_title">归属值</a-col>
            <a-col :span="12" class="table_title">证据</a-col>
          </a-row>
          <a-row class="table" v-for="(item, index) in data.other" :key="index">
            <a-col :span="4" class="table_value">
              {{ item.type }}
              <a-popover :title="item.subnet">
                <span v-if="item.subnet" class="span1"> 同子网 </span>
              </a-popover>
              <a-popover :title="item.topology">
                <span v-if="item.topology" class="span2"> 同拓扑 </span>
              </a-popover>
              <a-popover :title="item.dns">
                <span v-if="item.dns" class="span3"> 同DNS </span>
              </a-popover>
            </a-col>
            <a-col :span="8" class="table_value">{{ item.owner }}</a-col>
            <a-col :span="12" class="table_value">{{ item.evidence }}</a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "SingleIPOwner",
  props: ["data"],
  data() {
    return {
      isLoading: true, //是否还在加载
    };
  },
};
</script>
<style scoped lang="scss">
.empty_container {
  width: 100%;
  height: 300px;
}

.recommend_container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  margin-top: -20px;
  font-size: 26px;
  font-weight: bold;
}

.info_container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.subTitle {
  position: relative;
  padding-left: 13px;
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: bold;
}

.subTitle:before {
  content: "";
  background-color: #05981d;
  width: 3px;
  height: 22px;
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -10px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

.table {
  font-size: 15px;
  text-align: left;

  .table_title {
    padding-top: 14px;
    padding-bottom: 14px;
    padding-left: 30px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: bold;
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f5f5f5;
  }

  .table_value {
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    height: 60px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 4px;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;

    span {
      cursor: pointer;
      font-size: 6px;
      padding: 1px 2px;
      margin-left: 5px;
      display: block;
      // background-color: rgba(104, 203, 248, 0.062);
      border-radius: 5px;
    }
    .span1 {
      border: 1px solid #87d068;
      color: #87d068;
    }
    .span2 {
      border: 1px solid #108ee9;
      color: #108ee9;
    }

    .span3 {
      border: 1px solid #f50;
      color: #f50;
    }
  }
}
</style>
