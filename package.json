{"name": "spaceN", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"ant-design-vue": "^1.7.4", "axios": "^0.21.1", "core-js": "^3.8.3", "echarts": "^5.3.3", "flag-icon-css": "^4.1.7", "flag-icons": "^6.6.6", "less": "^3.13.1", "less-loader": "^8.0.0", "qs": "^6.11.0", "unzip": "^0.1.11", "vue": "2.7.16", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.3", "vue-echarts": "^6.2.3", "vue-i18n": "^8.26.3", "vue-multiselect": "^2.1.7", "vue-router": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/preload-webpack-plugin": "^2.0.0", "archiver": "^5.3.1", "compression-webpack-plugin": "^10.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "express": "^4.18.2", "html-webpack-plugin": "^5.5.3", "http-proxy-middleware": "^2.0.6", "image-minimizer-webpack-plugin": "^3.8.3", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-optipng": "^8.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^10.0.1", "proxy": "^1.0.2", "sass": "^1.92.0", "sass-loader": "^10.3.1", "ssh2": "^1.11.0", "svgo": "^3.0.2", "vue-template-compiler": "2.7.16", "webpack-bundle-analyzer": "^4.9.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}