var express = require("express");
var proxy = require("http-proxy-middleware");
var app = express();

app.use(
  "/api",
  proxy({
    target: "http://*************:8001", // 目标代理地址

    changeOrigin: true,
    //pathRewrite: {
    //    '^/api': ''
    //}
  })
);

app.use(express.static("dist"));

app.get("*", function(req, res) {
  res.sendFile("./dist/index.html");
});


//监听端口为1587
app.listen(3666, function() {
  console.log(" app listening at http://localhost:3666");
});
