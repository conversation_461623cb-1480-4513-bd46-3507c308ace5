//用于模拟接口，前端写界面的时候测试用
export function getSingleIp1(ip) {
  const data = [
    {
      key: "1",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
    {
      key: "2",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
    {
      key: "3",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
  ];
  return new Promise(function (resolve, reject) {
    if (ip === "*******") {
      setTimeout(() => {
        resolve(data);
      }, 2000);
    } else {
      setTimeout(() => {
        reject("err");
      }, 2000);
    }
  });
}

export function getSingleIp2(ip) {
  return new Promise((resolve, reject) => {
    if (ip === "*******") {
      setTimeout(() => {
        resolve(null);
      }, 3000);
    } else {
      setTimeout(() => {
        reject("err");
      }, 3000);
    }
  });
}

export function getMultiIp(ips) {
  const data = [
    {
      key: "1",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
    {
      key: "2",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
    {
      key: "3",
      ip: "*******",
      name: "xxx",
      source: "xxx",
    },
  ];
  return new Promise(function (resolve, reject) {
    if (ips.length > 1) {
      setTimeout(() => {
        resolve(data);
      }, 2000);
    } else {
      setTimeout(() => {
        reject("err");
      }, 2000);
    }
  });
}

export function getSearchData(value) {
  return new Promise((resolve, reject) => {
    if (value != "") {
      let search_list = [
        String.fromCharCode(65 + Math.ceil(Math.random() * 25)).repeat(4),
        String.fromCharCode(65 + Math.ceil(Math.random() * 25)).repeat(2),
        String.fromCharCode(65 + Math.ceil(Math.random() * 25)).repeat(3),
      ];
      setTimeout(() => {
        resolve(search_list);
      }, 500);
    } else {
      reject("err");
    }
  });
}
