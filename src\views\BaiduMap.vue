<template>
  <div id="baiduMap">
    <baidu-map
      ref="map"
      class="bm-view"
      :center="center"
      :zoom="zoom"
      :scroll-wheel-zoom="true"
      ak="Q1tkm4GzWyi0Pt4erbnXBXOvRNl69Opd"
    >
      <bm-scale anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-scale>
      <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-navigation>
      <bm-overview-map
        anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        :is-open="true"
      ></bm-overview-map>
      <bm-geolocation
        anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        :show-address-bar="true"
        :auto-location="true"
      ></bm-geolocation>
      <bm-circle
        v-show="circleShow"
        :center="circlePath.center"
        :radius="circlePath.radius"
        :stroke-opacity="0.5"
        :stroke-weight="2"
      ></bm-circle>
      <!--      搜索结果海量点-->
      <bml-marker-clusterer :averageCenter="true">
        <bm-marker
          v-for="(point, index) of points"
          :key="index"
          :position="{ lng: point.lng, lat: point.lat }"
          @click="infoWindowOpen(point)"
        >
          <bm-info-window
            :show="point.pointShow"
            :position="{ lng: point.lng, lat: point.lat }"
            @close="infoWindowClose(point)"
            :closeOnClick="false"
          >
            <div class="spinClass" v-if="!point.contentShow">
              <a-spin />
            </div>
            <a-table
              v-else
              :columns="columns"
              :data-source="pointBar"
              bordered
              :pagination="false"
              style="margin: 12px"
            >
              <span slot="item" slot-scope="text">
                <span v-if="text === '同源IP'">
                  {{ text }}
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>
                        同源IP：显示和这个设备在同一个路由后的其他IP
                      </span>
                    </template>
                    <a-icon type="question-circle" theme="twoTone" />
                  </a-tooltip>
                </span>
                <span v-else-if="text === '网络拓扑'">
                  {{ text }}
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>
                        网络拓扑：显示从所有探测点到该设备的网络拓扑
                      </span>
                    </template>
                    <a-icon type="question-circle" theme="twoTone" />
                  </a-tooltip>
                </span>
                <span v-else-if="text === '人员情报'">
                  {{ text }}
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>
                        人员情报：显示曾经通过该IP设备登录/访问过互联网服务的人员的相关情报
                      </span>
                    </template>
                    <a-icon type="question-circle" theme="twoTone" />
                  </a-tooltip>
                </span>
                <span v-else-if="text === '服务信息'">
                  {{ text }}
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>
                        服务信息：显示该服务的空间测绘信息，包括服务类型等属性
                      </span>
                    </template>
                    <a-icon type="question-circle" theme="twoTone" />
                  </a-tooltip>
                </span>
                <span v-else>
                  {{ text }}
                </span>
              </span>
              <span slot="content" slot-scope="record">
                <span v-if="typeof record === 'string'">{{ record }}</span>
                <div v-if="typeof record === 'object'">
                  <span v-if="record.length === 0">-</span>
                  <a-popover v-else placement="top" overlayClassName="toolTip">
                    <template slot="content">
                      <div v-for="(item, idx1) in record" :key="idx1">
                        应用产品：{{ item.product || "-" }}，版本：{{
                          item.version || "-"
                        }}，漏洞：
                        <span v-if="item.vulnerability.length < 1">-</span>
                        <span v-else>
                          <template
                            v-for="(bug, idx2) in item.vulnerability"
                            :key="idx2"
                          >
                            {{ bug.cve_id }}
                            <a-tag color="#f50">
                              {{ bug.level }}
                            </a-tag>
                          </template>
                        </span>
                      </div>
                    </template>
                    <a-tag color="green"> 共{{ record.length }}个 </a-tag>
                  </a-popover>
                </div>
              </span>
              <span slot="action" slot-scope="text">
                <span
                  v-if="text !== '-'"
                  style="cursor: pointer; color: #0072ee"
                >
                  <span
                    v-if="text === '同源IP'"
                    @click="showHomologousModal = true"
                    >详细信息</span
                  >
                  <span v-else-if="text === '网络拓扑'" @click="showTree = true"
                    >详细信息</span
                  >
                  <span
                    v-else-if="text === '人员情报'"
                    @click="showPersonelInformation"
                    >详细信息</span
                  >
                  <span
                    v-else-if="text === '服务信息'"
                    @click="showEquipment(point.ip)"
                    >详细信息</span
                  >
                </span>
                <span v-else> - </span>
              </span>
            </a-table>
          </bm-info-window>
        </bm-marker>
      </bml-marker-clusterer>
      <!--      同源点展示-->
      <bm-marker
        v-for="(point, index) in homologousList"
        :key="index"
        :position="{ lng: point.lng, lat: point.lat }"
        :icon="{
          url: require('@/assets/img/blueMarker.png'),
          opts: { imageSize: { width: 25, height: 25 } },
          size: { width: 25, height: 25 },
        }"
        @click="infoWindowOpen(point)"
      >
        <bm-info-window
          :show="point.pointShow"
          :position="{ lng: point.lng, lat: point.lat }"
          @close="infoWindowClose(point)"
          :closeOnClick="false"
        >
          <div class="spinClass" v-if="!point.contentShow">
            <a-spin />
          </div>
          <a-table
            v-else
            :columns="columns"
            :data-source="pointBar"
            bordered
            :pagination="false"
            style="margin: 12px"
          >
            <span slot="item" slot-scope="text" style="margin-left: -40px">
              <span v-if="text === '同源IP'">
                {{ text }}
                <a-tooltip placement="right">
                  <template slot="title">
                    <span> 同源IP：显示和这个设备在同一个路由后的其他IP </span>
                  </template>
                  <a-icon type="question-circle" theme="twoTone" />
                </a-tooltip>
              </span>
              <span v-else-if="text === '网络拓扑'">
                {{ text }}
                <a-tooltip placement="right">
                  <template slot="title">
                    <span> 网络拓扑：显示从所有探测点到该设备的网络拓扑 </span>
                  </template>
                  <a-icon type="question-circle" theme="twoTone" />
                </a-tooltip>
              </span>
              <span v-else-if="text === '人员情报'">
                {{ text }}
                <a-tooltip placement="right">
                  <template slot="title">
                    <span>
                      人员情报：显示曾经通过该IP设备登录/访问过互联网服务的人员的相关情报
                    </span>
                  </template>
                  <a-icon type="question-circle" theme="twoTone" />
                </a-tooltip>
              </span>
              <span v-else-if="text === '服务信息'">
                {{ text }}
                <a-tooltip placement="right">
                  <template slot="title">
                    <span>
                      服务信息：显示该服务的空间测绘信息，包括服务类型等属性
                    </span>
                  </template>
                  <a-icon type="question-circle" theme="twoTone" />
                </a-tooltip>
              </span>
              <span v-else>
                {{ text }}
              </span>
            </span>
            <span slot="content" slot-scope="record">
              <span v-if="typeof record === 'string'">{{ record }}</span>
              <div v-if="typeof record === 'object'">
                <span v-if="record.length === 0">-</span>
                <a-popover v-else placement="top" overlayClassName="toolTip">
                  <template slot="content">
                    <div v-for="(item, idx1) in record" :key="idx1">
                      应用产品：{{ item.product || "-" }}，版本：{{
                        item.version || "-"
                      }}，漏洞：
                      <span v-if="item.vulnerability.length < 1">-</span>
                      <span v-else>
                        <template
                          v-for="(bug, idx2) in item.vulnerability"
                          :key="idx2"
                        >
                          {{ bug.cve_id }}
                          <a-tag color="#f50">
                            {{ bug.level }}
                          </a-tag>
                        </template>
                      </span>
                    </div>
                  </template>
                  <a-tag color="green"> 共{{ record.length }}个 </a-tag>
                </a-popover>
              </div>
            </span>
            <span slot="action" slot-scope="text">
              <span v-if="text !== '-'" style="cursor: pointer; color: #0072ee">
                <span
                  v-if="text === '同源IP'"
                  @click="showHomologousModal = true"
                  >详细信息</span
                >
                <span v-else-if="text === '网络拓扑'" @click="showTree = true"
                  >详细信息</span
                >
                <span
                  v-else-if="text === '人员情报'"
                  @click="showPersonelInformation"
                  >详细信息</span
                >
                <span
                  v-else-if="text === '服务信息'"
                  @click="showEquipment(point.ip)"
                  >详细信息</span
                >
              </span>
              <span v-else> - </span>
            </span>
          </a-table>
        </bm-info-window>
      </bm-marker>
    </baidu-map>
    <!--    搜索按钮-->
    <div class="searchButton">
      <a-icon
        type="global"
        :style="{ fontSize: '24px', color: '#9BCFFA', cursor: 'pointer' }"
      />
      <input
        type="text"
        placeholder="请输入查询内容"
        class="searchInput"
        id="searchValue"
        v-model="query"
      />
      <div class="searchLine">
        <a-tooltip
          placement="bottom"
          style="position: absolute; right: -5px; top: -25px"
        >
          <template slot="title">
            <span>搜索</span>
          </template>
          <a-icon
            type="search"
            :style="{ fontSize: '24px', color: '#6FBA2C', cursor: 'pointer' }"
            @click="searchMap('')"
          />
        </a-tooltip>
      </div>
      <a-tooltip
        placement="bottom"
        style="padding-left: 12px; border-left: 1px solid #d8dae3"
      >
        <template slot="title">
          <span>暴露面查询下载</span>
        </template>
        <a-icon
          type="download"
          :style="{ fontSize: '24px', color: '#6FBA2C', cursor: 'pointer' }"
          @click="showDownloadExposedModal = true"
        />
      </a-tooltip>
    </div>
    <!--    搜索版面提示-->
    <div class="searchCard">
      <span>您可以查找以下内容:</span>
      <ol style="margin: 0; padding: 12px 0 0 16px; color: #222">
        <li>
          基于公司名字查找资产列表
          <br />
          如 <a @click="searchMap(owner)">{{ owner }}</a>
        </li>
        <li>
          基于公司邮箱查找登录IP
          <br />
          如<a @click="searchMap(mail)">{{ mail }}</a>
          <br />
        </li>
        <li>
          基于人员姓名查找登录IP
          <br />
          如 <a @click="searchMap(name)">{{ name }}</a>
        </li>
        <li>
          查找IP攻击面/风险路径
          <br />
          如 <a @click="searchMap(ip)">{{ ip }}</a>
        </li>
      </ol>
    </div>
    <!--    展示网络拓扑弹窗-->
    <IPTopology
      v-show="showTree"
      :treeModalVisible="showTree"
      @closeTreeModal="showTree = false"
      :ip="pointBar[0].content"
    />
    <!--    展示人员情报弹窗-->
    <Personnel
      :personnelModalVisible="showPersonnelModal"
      @closePersonnelModal="showPersonnelModal = false"
    />
    <!--    展示同源设备弹窗-->
    <Equipment
      :equipmentModalVisible="showHomologousModal"
      @closeEquipmentModal="showHomologousModal = false"
      @addHomologous="addHomologous"
    />
    <DownloadExposedModal
      v-show="showDownloadExposedModal"
      :visible="showDownloadExposedModal"
      :q="query"
      @close="showDownloadExposedModal = false"
    />
  </div>
</template>

<script>
import {
  BaiduMap,
  BmCircle,
  BmGeolocation,
  BmInfoWindow,
  BmlMarkerClusterer,
  BmMarker,
  BmNavigation,
  BmOverviewMap,
  BmScale,
} from "vue-baidu-map";
import { message } from "ant-design-vue";
import IPTopology from "@/components/IPTopology";
import Personnel from "@/components/Personnel";
import Equipment from "@/components/Equipment";

import DownloadExposedModal from "@/components/DownloadExposedModal";
import { testIp } from "@/util/regx";
import { getBaiduMap, getRedPoint } from "@/api/baiduMap";
// import Vue from "vue";

export default {
  name: "BaiduMapShow",
  components: {
    DownloadExposedModal,
    IPTopology,
    Personnel,
    Equipment,
    BaiduMap,
    BmScale,
    BmNavigation,
    BmOverviewMap,
    BmGeolocation,
    BmInfoWindow,
    BmlMarkerClusterer,
    BmMarker,
    BmCircle,
  },
  data() {
    return {
      query: "",
      points: [],
      homologousList: [], // 已展示在地图上的同源IP
      center: "中国",
      zoom: 0,
      circlePath: {
        center: {
          lng: 129,
          lat: 26,
        },
        radius: 200,
      },
      circleShow: false,
      showTree: false, // 展示网络拓扑
      showPersonnelModal: false, // 展示人员情报
      showHomologousModal: false, // 展示同源IP
      showDownloadExposedModal: false, // 暴露面下载数据
      ip: 'ip="**************"', // ip搜索模板
      owner: 'owner="Taiwan Semiconductor Manufacturing Company Limited"', // owner搜索模板
      name: 'name="ian cheng"', // name搜索模板
      mail: 'mail="@asiayo.com"', // mail模糊搜索
      columns: [
        {
          title: "信息项",
          dataIndex: "item",
          className: "column-item",
          scopedSlots: { customRender: "item" },
          align: "center",
        },
        {
          title: "描述/内容",
          dataIndex: "content",
          scopedSlots: { customRender: "content" },
          align: "center",
        },
        {
          title: "操作",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
          align: "center",
        },
      ],
      // 地图上的点展示面板内容
      pointBar: [
        {
          key: "1",
          item: "IP地址",
          content: "",
          action: "-",
        },
        {
          key: "2",
          item: "同源IP",
          content: "",
          action: "同源IP",
        },
        {
          key: "3",
          item: "网络拓扑",
          content: "",
          action: "网络拓扑",
        },
        {
          key: "4",
          item: "人员情报",
          content: "",
          action: "人员情报",
        },
        {
          key: "5",
          item: "服务信息",
          content: "",
          action: "服务信息",
        },
      ],
    };
  },
  methods: {
    // 往地图上增加点
    addPoints(datas) {
      for (let i = 0; i < datas.length; i++) {
        const point = {
          lng: parseFloat(datas[i].longitude),
          lat: parseFloat(datas[i].latitude),
          ip: datas[i].ip,
          pointShow: false, // 是否展示当前红点
          contentShow: false, // 是否展示当前面板内容
        };
        this.points.push(point);
      }
    },
    // 信息窗口打开后
    infoWindowOpen(point) {
      point.pointShow = true;
      // 请求红点面板信息
      getRedPoint({
        ip: point.ip,
      })
        .then((res) => {
          this.pointBar[0].content = res.data.data.ip;
          // 将当前操作IP存在本地
          sessionStorage.setItem("ip", res.data.data.ip);
          this.pointBar[1].content = `数量：${res.data.data.homologous}个`;
          this.pointBar[2].content = `数量：${res.data.data.path}条`;
          this.pointBar[3].content = `数量：${res.data.data.personnel}条`;
          this.pointBar[4].content = res.data.data.info;
          point.contentShow = true;
        })
        .catch((err) => {
          console.log("getRedPoint err", err);
        });
      if (point.iconColor === "blue") return;
      this.center = point;
      this.zoom = 20;
      this.circleShow = true;
      this.circlePath.center = point;
    },
    infoWindowClose(point) {
      point.pointShow = false;
      this.circleShow = false;
    },
    // 在地图上增加同源设备节点
    addHomologous(ip) {
      // 地图上已存在该同源节点
      let found = false;
      this.homologousList.forEach((item) => {
        if (item.ip === ip) {
          this.$message.info("该点同源IP已展示在地图上");
          found = true;
        }
      });
      if (!found) {
        let obj = {};
        obj.ip = ip;
        obj.lat =
          parseFloat(this.center.lat) +
          Math.random() * (0.0025 - 0.0001) -
          0.0015;
        obj.lng =
          parseFloat(this.center.lng) +
          Math.random() * (0.0025 - 0.0001) -
          0.0015;
        obj.pointShow = false;
        obj.iconColor = "blue";
        this.homologousList.push(obj);
        this.$message.success("该点同源IP已展示在地图上");
      }
    },
    // 用户在地图上进行搜索
    searchMap(searchValue) {
      this.homologousList = [];
      this.points = [];
      this.center = "中国";
      this.zoom = 0;
      this.circleShow = false;
      let hide = message.loading("正在搜索", 0);
      // 用户手动搜索
      if (searchValue === "") {
        searchValue = this.query;
        // 用户只输入了ip，自动补全搜索语法
        if (testIp(searchValue)) {
          searchValue = `ip="${searchValue}"`;
          this.query = searchValue;
        } else {
          const ipReg =
            /^ip="((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(\/\d{1,2})*)"$/;
          const ownerReg = /^owner="(.*)"$/;
          const nameReg = /^name="(.*)"$/;
          const mailWholeReg =
            /^mail="([A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5})"$/;
          const mailReg = /^mail="(@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5})"$/;
          // 用户输入符合规范
          if (
            ipReg.test(searchValue) ||
            ownerReg.test(searchValue) ||
            nameReg.test(searchValue) ||
            mailWholeReg.test(searchValue) ||
            mailReg.test(searchValue)
          ) {
            // 进行搜索
          } else {
            hide();
            this.$message.error("请按照语法规范进行检索");
          }
        }
      }
      // 用户点击提示例模板搜索
      else {
        this.query = searchValue;
      }
      getBaiduMap({
        query: btoa(unescape(encodeURIComponent(searchValue))),
      })
        .then((res) => {
          // 返回结果不为空
          if (res.data.data.length > 0) {
            this.addPoints(res.data.data); // 在地图上渲染结果
            hide(); // 隐藏“加载地图中……”的提示
            this.$message.success("搜索完毕！");
          } else {
            hide(); // 隐藏“加载地图中……”的提示
            this.$message.error("搜索结果为空！");
          }
        })
        .catch((err) => {
          hide();
          console.log("getBaiduMap err", err);
        });
    },
    // 展示服务信息
    showEquipment(ip) {
      // 跳转到资产列表页面
      let routeUrl = this.$router.resolve({
        path: "/search",
        query: {
          query: `ip="${ip}"`,
          time: new Date().getTime(), // 当前时间戳
          searchWay: "syntax", // 当前搜索方式
        },
      });
      window.open(routeUrl.href, "_blank");
    },
    // 展示人员情报
    showPersonelInformation() {
      // TODO:当前欠缺人员情报接口
      // if (this.showPoint.personnel.length > 0) {
      //   this.showPersonnelModal = true;
      // } else {
      //   this.$message.info('当前ip不存在人员情报列表');
      // }
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
#baiduMap {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;

  .spinClass {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300px;
    height: 330px;
    background: rgba(0, 0, 0, 0.05);
  }

  .bm-view {
    width: 100%;
    height: 89vh;
  }

  .searchButton {
    position: absolute;
    top: 50px;
    left: 50px;
    height: 45px;
    background-color: white;
    box-shadow: 0 2px 2px rgb(0 0 0 / 15%);
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-radius: 25px;

    .searchInput {
      border: none;
      background: none;
      outline: none;
      margin: 0 12px;
      width: 250px;
      padding: 0;
      color: #222;
      line-height: 45px;
      padding-right: 27px;

      &::placeholder {
        color: #ccc;
        font-weight: 400;
      }

      &::selection {
        background: rgba(94, 165, 247, 0.3);
      }
    }

    .searchLine {
      position: absolute;
      left: 52px;
      bottom: 8px;
      width: 250px;
      height: 1px;
      background-color: #d8dae3;
    }
  }

  .searchCard {
    position: absolute;
    top: 110px;
    left: 50px;
    width: 362px;
    padding: 24px;
    background-color: white;
    box-shadow: 0 2px 2px rgb(0 0 0 / 15%);
  }
}
</style>

<style>
th.column-item {
  padding-left: 8px !important;
}
</style>
