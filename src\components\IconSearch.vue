<template>
    <a-card class="searchBanner" title="请通过以下任何一种方式进行图标搜索" hoverable>
        <!--        卡片右上角的操作区域-->
        <template slot="extra">
            <!-- 关闭按钮，需要一个传参 -->
          <a-icon type="close" @click="$emit('icon-showstatus-update')"/>
        </template>
        <span style="display:inline-block;margin-bottom: 4px">方式一：粘贴icon地址</span>
        <a-input-search placeholder="请在此处粘贴icon的地址" style="width: 30em;margin-bottom: 8px"
                        @keyup.enter="IconUrlHash" @search="IconUrlHash"/>
        <span style="display:inline-block;margin-bottom: 4px">方式二：上传icon文件</span>
        <a-upload-dragger
            name="file"
            action="/api/v1/icon"
            @change="uploadIconStatus"
            :before-upload="checkIconTypeAndSize"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="picture"/>
          </p>
          <p class="ant-upload-text">
            将文件拖拽至此处上传或者点击上传
          </p>
          <p class="ant-upload-hint">
            仅支持256k以下.png .jpg .ico .jpeg图片格式
          </p>
        </a-upload-dragger>
      </a-card>
</template>
<script>
import {getIconUrlHash} from "@/api/home";
import {message} from "ant-design-vue";
import {testDomain, testUrl} from "@/util/regx";

export default {
  emits: ['icon-status-upload', 'icon-hash-upload', 'icon-showstatus-update'],
  methods: {
      // 图标url获取哈希值
    IconUrlHash(value) {
      this.$emit('icon-showstatus-update');
      let hide = message.loading('正在搜索', 0);
      let iconUrl = value;
      // 用户输入符合url格式
      // if (testUrl(value)) {
      //   // 缺少图标文件名
      //   if (!['png', 'jpg', 'ico', 'jpeg'].includes(value.split('.').pop().toLowerCase()) ) {
      //     iconUrl = value + '/favicon.ico';
      //   }
      // }
      // // 用户输入了域名需要自动补全
      // else if (testDomain(value)) {
      //   iconUrl = 'https://' + value + '/favicon.ico';
      // } else {
      if (!testUrl(value)&&!testDomain(value)){
        hide();
        message.error('请填写完整的url或者域名信息', 3);
        return
      }
      getIconUrlHash({
        url: iconUrl
      }).then((res) => {
        hide();
        if (res.data.code==10007){
          message.info('下载icon图片失败', 3);
        } else if (res.data.code==0){
          sessionStorage.setItem('icon', 'data:image/png;base64,' + res.data.icon);
          this.$emit('icon-hash-upload', res.data.hash);
        }
      }).catch(() => {
        hide();
        message.error('请检查输入的url或者域名是否正确', 3);
      })
    },
    // 上传图标文件
    uploadIconStatus(info) {
      const status = info.file.status;
      if (status === 'done') {
        message.success(`${info.file.name} 文件上传成功！`, 1);
        if (info.file.response.code==10006){
          message.info(`图片解析错误`, 3);
        } else if (info.file.response.code==0){
          // 图标的base64保存本地
          sessionStorage.setItem('icon', 'data:image/png;base64,' + info.file.response.icon);
          this.$emit('icon-status-upload', info);
        }
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败！`, 1);
      }
    },
    // 限制用户上传的图片格式和大小
    checkIconTypeAndSize(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/x-icon' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        this.$message.error('仅支持.png .jpg .ico .jpeg图片格式!');
      }
      const isLt256k = file.size / 1024 < 256;
      if (!isLt256k) {
        message.error('仅支持256k以下图片尺寸!');
      }
      return isJpgOrPng && isLt256k;
    },
  },
    
}
</script>
<style lang="less" scoped>
.searchBanner {
      width: 36em;
      position: absolute;
      top: 80px;
      left: 188px;
      z-index: 10;
}
</style>