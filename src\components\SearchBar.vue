<template>
  <div class="outerBar">
    <div
      class="searchBar"
      :style="{
        backgroundColor: showResult ? 'rgba(165, 212, 173, 0.1)' : '#fff',
      }"
    >
      <div class="inputOutter">
        <!--      不同检索方式-->
        <div
          style="
            width: 140px;
            padding-right: 0;
            border-right: 2px solid #909399;
            display: flex;
            justify-content: space-around;
          "
        >
          <a-tooltip placement="top">
            <template #title>
              <span>语法检索</span>
            </template>
            <a-icon
              type="bars"
              @click="
                searchWay = 'syntax';
                showIconBar = false;
                showMulBar = false;
                searchInputValue = '';
                icon = '';
              "
              :style="{
                fontSize: '26px',
                color: searchWay === 'syntax' ? '#6FBA2C' : '#595959',
                cursor: 'pointer',
                marginRight: '8px',
              }"
            />
          </a-tooltip>
          <a-tooltip placement="top">
            <template #title>
              <span>图标检索</span>
            </template>
            <a-icon
              type="picture"
              @click="
                searchWay = 'icon';
                showIconBar = true;
                showMulBar = false;
                searchInputValue = '';
              "
              :style="{
                fontSize: '26px',
                color: searchWay === 'icon' ? '#6FBA2C' : '#595959',
                cursor: 'pointer',
                marginRight: '8px',
              }"
            />
          </a-tooltip>

          <!-- <a-tooltip placement="top">
         <template slot="title">
           <span>批量检索</span>
         </template>
         <a-icon
             type="folder"
             @click="searchWay='multi';showMulBar=true;showIconBar=false;searchInputValue='';icon=''"
             :style="{ fontSize: '26px', color: searchWay==='multi'?'#6FBA2C':'#595959', cursor: 'pointer', marginRight: '8px' }"/>
       </a-tooltip> -->
        </div>
        <!-- 用户搜索的图标 -->
        <!-- <img v-show="icon!==''" :src="icon" alt="" style="position:absolute;left: 210px;width: 30px"> -->
        <img v-show="icon !== ''" :src="icon" alt="" />
        <!-- 这是搜索框 -->
        <!-- TODO:样式整理 -->
        <!-- 语法搜索 -->
        <!-- <a-auto-complete
        v-model="searchInputValue"
        :data-source="searchDataSource"
        style="flex-grow: 1; background-color: transparent; font-size: 16px"
        :defaultActiveFirstOption="false"
        @search="debounceAutoSearch"
        @change="onChange"
      >
        <input
          type="text"
          :placeholder="$t('home.search_placeholder')"
          :style="{ paddingLeft: icon ? '10px' : '20px' }"
          style="
            /* flex-grow: 1; */
            border: 0;
            background-color: transparent;
            outline: 0;
          "
          @keyup.enter="debounceSearch"
          @focus="showBanner"
        />
      </a-auto-complete> -->
        <input
          type="text"
          :placeholder="$t('home.search_placeholder')"
          :style="{ paddingLeft: icon ? '10px' : '20px' }"
          style="
            flex-grow: 1;
            border: 0;
            background-color: transparent;
            outline: 0;
            border-right: 2px solid #909399;
          "
          v-model="searchInputValue"
          @keyup.enter="debounceSearch"
          @focus="showBanner"
        />

        <!--      点击测绘搜索图标-->
        <a-tooltip placement="top">
          <template #title>
            <span>搜索</span>
          </template>
          <a-icon
            type="search"
            style="
              margin-right: 12px;
              font-size: 30px;
              color: #6fba2c;
              padding-left: 20px;
            "
            @click="debounceSearch"
          />
        </a-tooltip>

        <!--      图标搜索版面-->
        <IconSearch
          v-show="showIconBar"
          @icon-status-upload="IconStatusUpload"
          @icon-hash-upload="IconHashUpload"
          @icon-showstatus-update="IconShowStatusUpdate"
        />
        <!--      批量搜索版面-->
        <a-card
          v-show="showMulBar"
          class="searchBanner"
          title="请通过以下任何一种方式进行图标搜索"
          hoverable
        >
          <!--        卡片右上角的操作区域-->
          <template slot="extra">
            <a-icon type="close" @click="showMulBar = false" />
          </template>
          <a-tabs default-active-key="1">
            <a-tab-pane key="1" tab="批量输入">
              <textarea
                cols="40"
                rows="5"
                style="resize: none; padding: 12px"
                placeholder="请输入IP/域名/网段，例如：
                              ************-255
                              ***********-*************
                              ***********/24
                              ***********/*************"
              ></textarea>
              <a-button type="primary" ghost style="margin-top: 16px">
                搜索
              </a-button>
            </a-tab-pane>
            <a-tab-pane key="2" tab="上传文件">
              <a-upload-dragger
                name="file"
                action="http://*************:8000/api/v1/icon"
                :before-upload="checkCSVTypeAndSize"
              >
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-text">
                  请将包含 批量IP 的 .csv 文件拖拽至此处上传或者点击上传
                </p>
                <p class="ant-upload-hint">
                  最多支持 1000 条 ipv4、ipv6 批量查询
                </p>
              </a-upload-dragger>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </div>
      <!--      查询语法-->
      <a-tooltip placement="top" v-show="showResult">
        <template #title>
          <span>查询语法</span>
        </template>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            padding-left: 26px;
            padding-right: 16px;
            cursor: pointer;
            color: #0a88ff;
          "
          @click="showQuerySyntax = true"
        >
          <a-icon
            type="question-circle"
            style="margin-right: 4px; font-size: 24px"
          />
        </div>
      </a-tooltip>
      <!-- 语法查询模板 -->
      <QuerySyntax
        :queryModalVisible="showQuerySyntax"
        @closeQueryModal="showQuerySyntax = false"
      />
    </div>
    <div
      v-show="!showResult"
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #85c4ff;
        font-size: 18px;
        padding-top: 15px;
      "
      @click="showQuerySyntax = true"
    >
      <a-icon
        type="question-circle"
        style="margin-right: 4px; font-size: 22px"
      />
      <span>查询语法</span>
    </div>
  </div>
</template>

<script>
import QuerySyntax from "@/components/QuerySyntax";
import IconSearch from "@/components/IconSearch";
import { debounce } from "@/util/tool";
import { testDomain, testIp } from "@/util/regx";
import { getSearchData } from "@/util/mock";

export default {
  name: "SearchBar",
  components: {
    IconSearch,
    QuerySyntax,
  },
  data() {
    return {
      showQuerySyntax: false, // 展示语法查询
      searchWay: "syntax", // 当前搜索方式
      showIconBar: false, // 展示图标搜索
      showMulBar: false, // 展示批量搜索
      showResult: false, //是否在结果页
      searchInputValue: "",
      icon: "",
      searchDataSource: [], //联想搜索框的数据来源
    };
  },
  methods: {
    // 跳转搜索页面
    goToSearch(value) {
      this.$router.push({
        path: "/search",
        query: {
          query: value,
          time: new Date().getTime(), // 当前时间戳
          searchWay: this.searchWay, // 当前搜索方式
        },
      });
    },

    addQuotesToQuery(value) {
      let index = 0; //全局索引，用于遍历输入字符串
      function handlepartition(question, p) {
        let idx = question.indexOf(p);
        if (idx !== -1) {
          // 检查等号前面是否有单引号，如有则跳过
          let beforeEquals = question.slice(0, idx).trim();
          if (beforeEquals.endsWith("'") || beforeEquals.endsWith('"')) {
            return [question, -1]; // -1表示不处理
          }

          //输入的是语法语句
          let front = question.slice(0, idx).trim(); // 取前面的字符
          let end = question.slice(idx + p.length).trim(); // 取后面的字符
          // if (!end.startsWith('"')) {
          //   end = '"' + end;
          // }
          // if (!end.endsWith('"')) {
          //   end = end + '"';
          // }

          // 如果end已经被引号包围，不再添加引号
          if (!end.startsWith('"') && !end.startsWith("'")) {
            end = '"' + end;
          }
          if (!end.endsWith('"') && !end.endsWith("'")) {
            end = end + '"';
          }
          question = front + p + end;
        }
        return [question, idx];
      }

      function handleValue(value) {
        let question = "";
        if (value.length > 0) {
          question = value.trim();

          let hasQuotes = false;
          let quoteType = "";
          let originalContent = question;

          // 如果值已经被单引号包围，直接返回
          if (
            (question.startsWith("'") && question.endsWith("'")) ||
            (question.startsWith('"') && question.endsWith('"'))
          ) {
            hasQuotes = true;
            quoteType = question.startsWith("'") ? "'" : '"';
            originalContent = question.slice(1, -1);
          }

          // 检查是否包含操作符（=, ==, !=）
          const partition = ["==", "!=", "="];
          let hasOperator = false;

          for (let p of partition) {
            let [temp, idx] = handlepartition(question, p);
            if (idx !== -1) {
              question = temp;
              hasOperator = true;

              return question;
            }
          }

          if (!hasOperator) {
            // console.log("输入非语句", originalContent);
            
            let state = "";
            //输入非语句，需要对ip、domian、body做专门校验
            if (testIp(originalContent)) {
              state = "IP";
            } else if (testDomain(originalContent)) {
              state = "Domain";
            } else {
              state = "Body";
            }

            switch (state) {
              case "IP":
                question = hasQuotes
                  ? `ip=${quoteType}${originalContent}${quoteType}`
                  : `ip="${originalContent}"`;
                break;
              case "Domain":
                question = hasQuotes
                  ? `domain=${quoteType}${originalContent}${quoteType}`
                  : `domain="${originalContent}"`;
                break;
              case "Body":
                question = hasQuotes
                  ? `body=${quoteType}${originalContent}${quoteType}`
                  : `body="${question}"`;
                break;
              default:
                break;
            }
            // handleQuestion(question);
          }
        }
        return question;
      }
      //递归解析器函数
      function parseQuery() {
        let result = "";
        while (index < value.length) {
          const char = value[index];
          if (char === "(") {
            // 处理左括号，递归解析括号内的子查询
            index++;
            const subQuery = parseQuery();
            result += `(${subQuery})`;
          } else if (char === ")") {
            // 处理右括号，返回结果
            index++;
            return result;
          } else if (char === "&" && value[index + 1] === "&") {
            //处理连接符 &&
            index += 2;
            result += " && ";
          } else if (char === "|" && value[index + 1] === "|") {
            index += 2;
            result += " || ";
          } else if (char === " " || char === "|" || char === "&") {
            index++;
          } else {
            //处理查询语句部分
            let idx1 = value.indexOf("&", index);
            let idx2 = value.indexOf("|", index);
            let idx3 = value.indexOf(")", index);

            const endOfQuery = idx1 !== -1 || idx2 !== -1 || idx3 !== -1;

            if (idx1 === -1) {
              idx1 = Infinity;
            }
            if (idx2 === -1) {
              idx2 = Infinity;
            }
            if (idx3 === -1) {
              idx3 = Infinity;
            }
            const query = endOfQuery
              ? value.slice(index, Math.min(idx1, idx2, idx3))
              : value.slice(index);
            result += handleValue(query);
            if (endOfQuery) {
              index += query.length;
            } else {
              index = value.length;
            }
          }
        }
        return result;
      }
      return parseQuery();
    },
    
    onSearch() {
      this.showIconBar = false;
      this.showMulBar = false;
      if (this.searchInputValue.length > 0) {
        this.searchWay = "syntax";
        let question = this.addQuotesToQuery(this.searchInputValue);
        this.goToSearch(question);
      }
    },
    debounceSearch: debounce(function () {
      this.onSearch();
    }, 500),
    // 搜索联想词
    onAutoSearch(value) {
      getSearchData(value, this.searchDataSource)
        .then((value) => {
          this.searchDataSource = value;
        })
        .catch(() => {
          this.searchDataSource = [];
        });
    },
    debounceAutoSearch: debounce(function () {
      if (this.searchInputValue.length >= 2) {
        this.onAutoSearch(this.searchInputValue);
      } else {
        this.searchDataSource = [];
      }
    }, 500),
    // onChange(value) {
    //   this.searchInputValue = value;
    // },
    // 限制用户上传的csv文件格式和大小
    checkCSVTypeAndSize(file) {
      const isCSV = file.type === "text/csv";
      if (!isCSV) {
        this.$message.error("仅支持.csv文件格式!");
      }
      const isLt256k = file.size / 1024 < 256;
      if (!isLt256k) {
        this.$message.error("仅支持256k以下文件大小!");
      }
      return isCSV && isLt256k;
    },
    // 图标搜索
    IconStatusUpload(info) {
      this.searchValue = `icon_hash_mmh3="${info.file.response.hash}"`;
      setTimeout(() => {
        this.searchWay = "icon";
        this.goToSearch(this.searchValue);
      }, 1500);
    },
    // 图标hash搜索
    IconHashUpload(info) {
      this.searchValue = `icon_hash_mmh3="${info}"`;
      this.goToSearch(this.searchValue);
    },
    IconShowStatusUpdate() {
      this.showIconBar = false;
      this.showMulBar = false;
    },
    // input聚焦展示面板
    showBanner() {
      if (this.searchWay === "icon") {
        this.showIconBar = true;
        this.showMulBar = false;
      } else if (this.searchWay === "multi") {
        this.showMulBar = true;
        this.showIconBar = false;
      } else {
        this.showMulBar = false;
        this.showIconBar = false;
      }
    },
  },
  watch: {
    // 监听路由变化获取参数
    $route: {
      handler: function (to) {
        if (to.name === "search") {
          this.showResult = true;
        } else {
          this.showResult = false;
        }
        if (to.query.query) {
          this.searchInputValue = to.query.query;
          this.searchWay = to.query.searchWay;
          if (this.searchWay === "syntax") {
            this.showIconBar = false;
            this.showMulBar = false;
            this.icon = "";
          } else if (this.searchWay === "icon") {
            this.icon = sessionStorage.getItem("icon");
          }
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.outerBar {
  width: 100%;
  background-color: transparent;
}
.searchBar {
  width: 100%;
  display: flex;
  // background-color: #fff;
  border: 1px solid #fff;
  // background-color: rgba(165, 212, 173, 0.1);
  border-radius: 15px;
  padding: 8px;
  box-shadow: 9px 9px 12px rgb(*********** / 20%),
    -9px -9px 12px rgb(*********** / 25%);
}
.inputOutter {
  position: relative;
  display: flex;
  width: 100%;
  padding: 12px 32px;
  border-radius: 10px;
  box-shadow: inset 10px 10px 15px 10px rgb(*********** / 30%),
    inset -10px -10px 15px -10px #fff;
  font-size: 16px;
  color: #333;

  img {
    display: inline-block;
    width: 30px;
    margin-left: 20px;
  }
}
.searchBanner {
  width: 36em;
  position: absolute;
  top: 80px;
  left: 188px;
  z-index: 10;
}
.ant-select-selection {
  background-color: transparent;
}
</style>
