<template>
  <div id="search">
    <SearchBar />
    <!-- 搜索结果 -->
    <main class="resultList">
      <!-- 资产统计 -->
      <AssetStatisticsArea
        ref="asset_statistics"
        :startTime="startTime"
        :endTime="endTime"
        :searchValue="searchValue"
        @addtag="addTag"
      />
      <!-- 资产列表 -->
      <div class="assetList">
        <!-- :default-value="[this.startTime, this.endTime]"
        :value="[this.startTime, this.endTime]" -->
        <!-- 过滤区 -->
        <div class="dataFilter">
          <div style="display: flex; align-items: center; width: 100%">
            <span class="font-3">{{ $t("search.time") }}</span>
            <!--          选择时间下拉菜单-->
            <a-range-picker
              show-time
              :ranges="{
                最近1个月: [
                  moment().startOf('day').subtract(1, 'months'),
                  moment(),
                ],
                最近半年: [
                  moment().startOf('day').subtract(6, 'months'),
                  moment(),
                ],
                最近一年: [
                  moment().startOf('day').subtract(1, 'years'),
                  moment(),
                ],
              }"
              :default-value="[
                startTime ? moment(startTime) : null,
                endTime ? moment(endTime) : null,
              ]"
              :value="[
                startTime ? moment(startTime) : null,
                endTime ? moment(endTime) : null,
              ]"
              format="YYYY-MM-DD"
              @ok="timeComfire"
              @change="timeChange"
              style="margin: 0 12px"
            />
            <div style="flex: 1">
              <a-button style="float: right" @click="showModal"
                >数据导出</a-button
              >
            </div>
            <!-- 数据导出弹窗 -->
            <a-modal
              v-model="download_visble"
              width="600px"
              title="数据导出"
              @ok="handleok"
            >
              <a-form
                :form="form"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 18 }"
              >
                <a-form-item label="文件名称">
                  <a-input v-model="file_name" />
                </a-form-item>
                <a-form-item label="文件格式">
                  <a-select default-value="csv" disabled> </a-select>
                </a-form-item>
                <a-form-item label="导出字段">
                  <a-select mode="multiple" v-model="checklist">
                    <div slot="dropdownRender">
                      <div
                        style="width: 100%; padding: 8px 0 5px 8px"
                        @mousedown="(e) => e.preventDefault()"
                      >
                        <a-checkbox-group
                          @change="onCheckChange"
                          v-model="checklist"
                        >
                          <a-row style="width: 100%">
                            <a-col :span="8">
                              <a-checkbox value="应用场景">应用场景</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="资产标签">资产标签</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="IP">IP</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="ASN归属">ASN归属</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="ISP运营商"
                                >ISP运营商</a-checkbox
                              >
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="端口">端口</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="协议">协议</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="URL">URL</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="域名">域名</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="应用/组件"
                                >应用/组件</a-checkbox
                              >
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="站点标题">站点标题</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="ICP备案企业"
                                >ICP备案企业</a-checkbox
                              >
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="ICP网站备案号"
                                >ICP网站备案号</a-checkbox
                              >
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="状态码">状态码</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="国家名">国家名</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="省份/州">省份/州</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="城市">城市</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="区县">曲线</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="banner哈希"
                                >banner哈希</a-checkbox
                              >
                            </a-col>

                            <a-col :span="8">
                              <a-checkbox value="图标哈希">图标哈希</a-checkbox>
                            </a-col>
                            <a-col :span="8">
                              <a-checkbox value="更新时间">更新时间</a-checkbox>
                            </a-col>
                          </a-row>
                        </a-checkbox-group>
                      </div>
                    </div>
                  </a-select>
                </a-form-item>
              </a-form>
            </a-modal>
          </div>
          <!-- 筛选标签列表  -->
          <div style="margin-top: 8px" v-show="tags.length > 0">
            <template v-for="tag in tags">
              <a-tag
                :key="tag"
                :closable="true"
                @close="() => handleClose(tag)"
              >
                {{ tag }}
              </a-tag>
            </template>
            <span
              v-if="tags.length > 0"
              @click="deleteAllTags"
              style="cursor: pointer"
            >
              <a-icon type="delete" style="margin-right: 4px" />
              {{ $t("search.empty") }}
            </span>
          </div>
        </div>
        <!-- 资产列表表格 -->
        <AssetListArea
          ref="asset_list"
          :startTime="startTime"
          :endTime="endTime"
          :searchValue="searchValue"
          @addtag="addTag"
        />
      </div>
    </main>
  </div>
</template>

<script>
import SearchBar from "@/components/SearchBar";
import AssetStatisticsArea from "@/components/AssetStatisticsArea";
import AssetListArea from "@/components/AssetListArea";
import { pagination_data, tags_data, date_data } from "@/util/sessionUtils";
import moment from "moment";

export default {
  name: "Search",
  components: {
    SearchBar,
    AssetStatisticsArea,
    AssetListArea,
  },
  data() {
    return {
      download_visble: false,
      file_name: "",
      checklist: [
        "IP",
        "端口",
        "协议",
        "URL",
        "域名",
        "国家名",
        "省份/州",
        "城市",
        "区县",
        "更新时间",
      ],
      searchValue: "", // 用户搜索关键词
      searchWay: "", // 用户当前检索方式
      usingTime: 0, // 搜索使用时间
      startTime: "",
      endTime: "",
      tags: [], // 新增搜索标签
      without_tags_value: "", //除去搜索标签外的value
      current: 1,
      pageSize: 10,

      form: this.$form.createForm(this),
    };
  },
  methods: {
    moment,
    showModal() {
      this.download_visble = true;
    },
    handleok() {
      // console.log(this.file_name);
      // console.log(this.checklist);
      // TODO:发post请求给后端提交下载任务
      let data = {
        //要发的数据
        file_name: this.file_name,
        query: this.$route.query,
        fields: this.checklist, //TODO：转成跟资产列表相同的对应字段名
        limit: 100, //TODO:改成该条查询语句搜索到的资产数目
      };
      console.log(data);
    },
    onCheckChange(checklist) {
      this.checklist = [...checklist];
    },
    determineCode(code) {
      if (code == 10002) {
        // 正常不会出现
        this.$message.info(`每页数据量超限, 最大值200`, 3);
      } else if (code == 10003) {
        this.$message.info(`数量超限, 最多可查看10000条数据`, 3);
      } else if (code == 10004) {
        this.$message.info(`输入值格式有误, 请排查语句标点符号等`, 3);
      } else if (code == 0) {
        return true;
      }
      return false;
    },
    // 确定时间范围
    timeComfire(dateString) {
      const timeFormat = "YYYY-MM-DD";
      this.startTime = dateString[0].format(timeFormat);
      this.endTime = dateString[1].format(timeFormat);
      date_data.saveDate(`${this.startTime} ${this.endTime}`);
      this.$refs.asset_list.setPagination("current", 1);
      this.$refs.asset_list.setPagination("pageSize", 10);
      [this.current, this.pageSize] = [1, 10];
      pagination_data.savePagination(`${this.current} ${this.pageSize}`);
      this.$refs.asset_list.getAssetListData(
        this.searchValue,
        this.startTime,
        this.endTime,
        this.current,
        this.pageSize
      );
      this.$refs.asset_statistics.getAssetStatisticsData(
        this.searchValue,
        this.all,
        this.startTime,
        this.endTime
      );
    },
    // 清除时间
    timeChange(dateString) {
      if (!dateString[0] && !dateString[1]) {
        // 获取当前时间和半年前的时间作为默认请求时间范围
        this.getDefualtTimeRange(6);
        // 获取资产列表数据
        this.$refs.asset_list.setPagination("current", 1);
        this.$refs.asset_list.setPagination("pageSize", 10);
        [this.current, this.pageSize] = [1, 10];
        pagination_data.savePagination(`${this.current} ${this.pageSize}`);
        this.$refs.asset_list.getAssetListData(
          this.searchValue,
          this.startTime,
          this.endTime,
          this.current,
          this.pageSize
        );
        // 获取资产统计数据
        this.$refs.asset_statistics.getAssetStatisticsData(
          this.searchValue,
          this.all,
          this.startTime,
          this.endTime
        );
      } else {
        const timeFormat = "YYYY-MM-DD";
        this.startTime = dateString[0].format(timeFormat);
        this.endTime = dateString[1].format(timeFormat);
      }
      date_data.saveDate(`${this.startTime} ${this.endTime}`);
    },
    // 增加搜索标签
    addTag(tag, text) {
      let newTag = "";
      if (tag === "asset_tags") {
        //资产标签需要单独处理
        switch (text.toLowerCase()) {
          case "honeypot":
            newTag = 'type="Honeypot"';
            break;
          case "cloud":
            newTag = 'is_cloud="true"';
            break;
          case "cdn":
            newTag = ' is_cdn="true"';
            break;
          default:
            break;
        }
      } else {
        newTag = tag + "=" + '"' + text + '"';
      }
      if (
        !this.tags.includes(newTag) &&
        this.searchValue.indexOf(newTag) === -1
      ) {
        this.tags.push(newTag);
        tags_data.saveTags(this.tags.join("&&"));
        // 请求语句拼接上筛选条件
        this.$router.push({
          path: "/search",
          query: {
            query: this.searchValue + ` && ${newTag}`,
            searchWay: this.searchWay,
            time: new Date().getTime(), // 当前时间戳
          },
        });
      } else {
        this.$message.warn("已添加该搜索标签");
      }
    },
    // 删除搜索标签
    handleClose(removedTag) {
      let tag = removedTag.slice(0, removedTag.indexOf("="));
      let text = removedTag.slice(removedTag.indexOf("=") + 1);
      let temp = [...this.tags];
      temp.splice(this.tags.indexOf(removedTag), 1);
      this.tags = [...temp];
      // this.tags = this.tags.splice(this.tags.indexOf(removedTag), 1);
      // console.log(this.tags);
      tags_data.saveTags(this.tags.join("&&"));
      this.$router.push({
        path: "/search",
        query: {
          query: this.searchValue.replace(` && ${tag}=${text}`, ""),
          searchWay: this.searchWay,
          time: new Date().getTime(), // 当前时间戳
        },
      });
    },
    // 清空搜索标签(保留请求初始值)
    deleteAllTags() {
      this.tags.forEach((v) => {
        let tag = v.slice(0, v.indexOf("="));
        let text = v.slice(v.indexOf("=") + 1);
        this.searchValue = this.searchValue.replace(` && ${tag}=${text}`, "");
      });
      this.tags = [];
      tags_data.saveTags(this.tags.join("&&"));
      this.$router.push({
        path: "/search",
        query: {
          query: this.searchValue,
          searchWay: this.searchWay,
          time: new Date().getTime(), // 当前时间戳
        },
      });
    },
    // 获取当前时间和几个月前的时间作为默认请求时间范围,参数n：几个月
    getDefualtTimeRange(n) {
      this.startTime = moment()
        .startOf("day")
        .subtract(n, "months")
        .format("YYYY-MM-DD");
      this.endTime = moment().format("YYYY-MM-DD");
    },

    isRemoveTag(newValue, oldValue) {
      let result = true;
      newValue.split("&&").forEach((v) => {
        if (oldValue.indexOf(v) === -1) {
          result = false;
        }
      });
      return result;
    },
    HandleTags(value) {
      let newTags = [];
      this.tags.forEach((v) => {
        if (value.indexOf(v) !== -1) {
          newTags.push(v);
        }
      });
      this.tags = [...newTags];
      tags_data.saveTags(this.tags.join("&&"));
    },
  },

  destroyed() {
    pagination_data.removePagination();
    date_data.removeDate();
    tags_data.removeTags();
    // 在这里remove掉
  },
  watch: {
    // 监听路由变化获取参数
    $route: {
      handler: function (to) {
        if (to.name === "search") {
          let pagination_ = pagination_data.getPagination();
          if (pagination_) {
            let [cur, page] = pagination_.split(" ");
            this.current = Number(cur);
            this.pageSize = Number(page);
          }
          let date = date_data.getDate();
          if (date) {
            let [start, end] = date.split(" ");
            this.startTime = start;
            this.endTime = end;
          } else {
            this.getDefualtTimeRange(6);
          }
          // 请求附上当前时间戳则代表需要刷新请求（包括新的搜索语句和刷新页面）
          if (to.query.time || to.query.query !== this.searchValue) {
            this.all = false;
            if (this.searchValue === "") {
              //页面刷新
              let tags = tags_data.getTags();
              if (tags && tags !== "") {
                this.tags = tags.split("&&");
              } else {
                this.tags = [];
              }
            } else {
              //搜索新的或增加/减少tag
              this.current = 1;
              this.pageSize = 10;
              if (
                this.seachValue !== "" &&
                to.query.query.indexOf(this.searchValue) === -1 &&
                !this.isRemoveTag(to.query.query, this.searchValue)
              ) {
                //新的搜索
                this.tags = [];
                tags_data.removeTags();
                // date_data.removeDate();
                // this.getDefualtTimeRange(6);
              } else {
                this.HandleTags(to.query.query);
              }
            }
            this.searchValue = to.query.query;
            // 用户检索新的关键词
            this.searchWay = to.query.searchWay;
            this.$nextTick(() => {
              const assetStatis = this.$refs.asset_statistics;
              const assetLs = this.$refs.asset_list;
              assetLs.setPagination("current", this.current);
              assetLs.setPagination("pageSize", this.pageSize);
              assetLs.pageSize = this.pageSize;
              if (assetStatis && assetLs) {
                assetStatis.getAssetStatisticsData(
                  this.searchValue,
                  this.all,
                  this.startTime,
                  this.endTime
                );
                assetLs.getAssetListData(
                  this.searchValue,
                  this.startTime,
                  this.endTime,
                  this.current,
                  this.pageSize
                );
              }
            });
          }
        } else if (to.name !== "search" && to.name !== "details") {
          //跳转到其他页面（非详情页）时需要卸载一下组件
          this.$destroy();
        }
      },
      immediate: true,
    },
  },
};
</script>

<style scoped lang="scss">
#search {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 100px);
  box-sizing: border-box;
  padding: 20px 32px 20px 32px;
  background-color: rgb(247, 249, 252);

  .resultList {
    flex-grow: 1;
    display: flex;
    height: 80%;
    box-sizing: border-box;
    margin-top: 20px;

    .assetList {
      flex-basis: 1000px;
      flex-grow: 1;
      height: 100%;
      overflow-y: auto;
      background-color: white;
      // padding: 12px;
      padding: 16px 20px;
      border-radius: 15px;
      box-shadow: 9px 9px 12px rgb(189 189 189 / 20%),
        -9px -9px 12px rgb(255 255 255 / 25%);

      .dataFilter {
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: rgb(245, 245, 245);
      }
    }
  }
}
</style>
