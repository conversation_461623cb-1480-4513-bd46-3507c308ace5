import request from "@/util/request";

// 获取资产列表数据
/*
 * query	必须	查询语法的组合,base64编码
 * page	可选	默认为1
 * page_size	可选	默认为10,最大值200
 * end_time	可选	默认为当天，格式年月日YYYY-MM-DD
 * start_time	可选	默认为当天的前一个月，格式年月日YYYY-MM-DD
 * */
export function getAssetList(params) {
  let url = "/api/v1/search";
  return request.get(url, { params: params });
}

// 获取资产统计数据
/*
 * query	必须	查询语法的组合
 * fields	可选	可为ip,country,port,icon,server,title,protocol组合的字符串，默认为ip,country,port	显示排名(数量)的字段
 * end_time	可选	默认为当天，格式年月日YYYY-MM-DD
 * start_time	可选	默认为当天的前一个月，格式年月日YYYY-MM-DD*/
export function getAssetStatistics(params) {
  let url = "/api/v1/search/total";
  return request.get(url, { params: params });
}

// 获取IP聚合数据
/*
 * port	必须	查询语法的组合
 * end_time	可选	默认为当天，格式年月日YYYY-MM-DD
 * start_time	可选	默认为当天的前一个月，格式年月日YYYY-MM-DD*/
export function getIPAggregation(q, params) {
  let url = "api/v1/host/";
  return request.get(url + q, { params: params });
}

// 获取资产详情
/*
 * port	必须*/
export function getIPDetail(q, params) {
  let url = "api/v1/detail/";
  return request.get(url + q, { params: params });
}

// 获取IP归属
export function getAttribution(params) {
  let url = "/api/v1/map/attribution_new";
  return request.get(url, { params: params });
}

export function getMultiAttribution(ips) {
  let url = "/api/v1/map/attribution_new";
  return request.post(url, `IP=${ips}`);
}
