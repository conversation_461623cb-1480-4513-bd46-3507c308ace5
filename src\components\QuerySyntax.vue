<template>
  <a-modal
    :visible="queryModalVisible"
    @cancel="queryClose"
    title="查询语法规范"
    width="700px"
    height="550px"
    :footer="null"
  >
    <div class="query">
      <p>
        搜索范围包括
        <a href="javascript:void(0)" @click="goAnchor('#ip')">ip-basic属性</a>、
        <a href="javascript:void(0)" @click="goAnchor('#location')"
          >location属性</a
        >、 <a href="javascript:void(0)" @click="goAnchor('#ssl')">ssl信息</a>、
        <a href="javascript:void(0)" @click="goAnchor('#http')"
          >http_response属性</a
        >、
        <a href="javascript:void(0)" @click="goAnchor('#info')">info字段</a>、
        <a href="javascript:void(0)" @click="goAnchor('#CA')">CA证书属性</a>
        等分类； 直接输入查询字将全局进行搜索。
      </p>
      <a-table
        :columns="columns"
        :data-source="connector"
        size="small"
        :pagination="false"
        bordered
      >
      </a-table>
      <a id="ip" class="classification font-3">ip-basic属性查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="IPData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <template v-if="typeof text === 'string'">
            <a @click="goToSearch(text)" :title="text">
              {{ text }}
            </a>
          </template>
          <template v-else>
            <span v-for="(item, index) in text" :key="index">
              <a @click="goToSearch(item)" :title="item">
                {{ item }}
              </a>
              <br />
            </span>
          </template>
        </span>
      </a-table>
      <a id="location" class="classification font-3">location属性查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="locationData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <a @click="goToSearch(text)" :title="text">
            {{ text }}
          </a>
        </span>
      </a-table>
      <a id="ssl" class="classification font-3">ssl信息查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="sslData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <a @click="goToSearch(text)" :title="text">
            {{ text }}
          </a>
        </span>
      </a-table>
      <a id="http" class="classification font-3">http_response属性查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="httpData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <a @click="goToSearch(text)" :title="text">
            {{ text }}
          </a>
        </span>
      </a-table>
      <a id="info" class="classification font-3">info字段查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="infoData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <a @click="goToSearch(text)" :title="text">
            {{ text }}
          </a>
        </span>
      </a-table>
      <a id="CA" class="classification font-3">CA证书属性查询语法</a>
      <a-table
        :columns="dataColumns"
        :data-source="CAData"
        size="small"
        :pagination="false"
        bordered
      >
        <span slot="grammar" slot-scope="text">
          <a @click="goToSearch(text)" :title="text">
            {{ text }}
          </a>
        </span>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: "QuerySyntax",
  props: {
    queryModalVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [
        {
          title: "连接符",
          dataIndex: "connector",
          width: "50px",
        },
        {
          title: "含义",
          dataIndex: "meaning",
        },
      ],
      dataColumns: [
        {
          title: "语法",
          dataIndex: "grammar",
          width: "300px",
          ellipsis: true,
          scopedSlots: { customRender: "grammar" },
        },
        {
          title: "用途说明",
          dataIndex: "usage",
        },
      ],
      connector: [
        {
          key: "1",
          connector: "=",
          meaning: "表示查询包含关键词的资产",
        },
        {
          key: "2",
          connector: "==",
          meaning: "表示查询有且仅有关键词的资产",
        },
        {
          key: "3",
          connector: "!=",
          meaning: "表示剔除包含关键词的资产",
        },
        {
          key: "4",
          connector: "&&",
          meaning: "同and，表示和",
        },
        {
          key: "5",
          connector: "||",
          meaning: "同or，表示或",
        },
        {
          key: "6",
          connector: "()",
          meaning: "表示查询优先级，在()中的优先级最高",
        },
      ],
      IPData: [
        {
          key: "1",
          grammar: 'ip="*******"',
          usage: '从ip中搜索包含"*******"的网站',
        },
        {
          key: "2",
          grammar: 'ip="*************/24"',
          usage: '查询IP为"*************"的C网段资产',
        },
        {
          key: "3",
          grammar: 'port="6379"',
          usage: "查找对应“6379”端口的资产",
        },
        {
          key: "4",
          grammar: 'protocol="quic"',
          usage: "查询quic协议资产",
        },
        {
          key: "5",
          grammar: 'banner="users"',
          usage: "搜索banner中带有users文本的资产",
        },
        {
          key: "6",
          grammar: 'banner_hash="50765930"',
          usage: '搜索banner_hash为"50765930"的资产',
        },
        {
          key: "7",
          grammar: 'rule="Microsoft-Exchange"',
          usage: "搜索Microsoft-Exchange设备",
        },
        {
          key: "8",
          grammar: 'transport_protocol="udp"',
          usage: "搜索指定udp协议的资产",
        },
        {
          key: "9",
          grammar: [
            'icon_hash="728788645"',
            'icon_hash="472290b058eaada7997b2d66d2e2c345"',
            'icon_hash_mmh3="728788645"',
            'icon_hash_md5="472290b058eaada7997b2d66d2e2c345"',
          ],
          usage: "搜索icon_hash的mmh3或md5对应的资产",
        },
      ],
      locationData: [
        {
          key: "1",
          grammar: 'isp="华为"',
          usage: "搜索相关网络服务提供商的资产",
        },
        {
          key: "2",
          grammar: 'cloud="谷歌云"',
          usage: "搜索云厂商名",
        },
        {
          key: "3",
          grammar: 'is_cloud="true"',
          usage: "搜索是云厂商的资产",
        },
        {
          key: "4",
          grammar: 'country ="中国"',
          usage: "搜索指定国家的资产",
        },
        {
          key: "5",
          grammar: 'country_code="cn"',
          usage: "搜索指定国家(编码)的资产(大小写均可)",
        },
        {
          key: "6",
          grammar: 'region="广东省"',
          usage: "搜索指定行政区的资产",
        },
        {
          key: "7",
          grammar: 'city="广州市"',
          usage: "搜索指定城市的资产",
        },
        {
          key: "8",
          grammar: 'area="海淀区"',
          usage: "搜索指定区县的资产",
        },
        {
          key: "9",
          grammar: 'asn="19551"',
          usage: "搜索指定asn的资产",
        },
        {
          key: "10",
          grammar: 'usage="数据中心"',
          usage: "搜索指定应用场景的资产",
        },
        {
          key: "11",
          grammar: 'owner="天威视讯"',
          usage: "搜索指定拥有者的资产",
        },
      ],
      sslData: [
        {
          key: "1",
          grammar: 'ssl_version="TLSv1.3"',
          usage: "搜索证书的ssl版本",
        },
        {
          key: "2",
          grammar:
            'ssl_jarm="07d2ad16d21d21d07c07d2ad07d21d31a9f9ff55c8eb531c837ecb7066da40"',
          usage: "搜索JARM指纹",
        },
        {
          key: "3",
          grammar: 'ssl_cipher_name="TLS_AES_128_GCM_SHA256"',
          usage: "搜索加密套件名称",
        },
        {
          key: "4",
          grammar: 'ssl_cipher_bits="128"',
          usage: "加密套件位数",
        },
      ],
      httpData: [
        {
          key: "1",
          grammar: 'title="beijing"',
          usage: "搜索网页标题含beijing的资产",
        },
        {
          key: "2",
          grammar: 'header="elastic"',
          usage: "搜索http响应头含elastic的资产",
        },
        {
          key: "3",
          grammar: 'cookie="HttpOnly"',
          usage: '搜索http响应头cookie字段含"HttpOnly"的资产',
        },
        {
          key: "4",
          grammar: 'body="网络空间测绘"',
          usage: '搜索http响应体含"网络空间测绘"的资产',
        },
        {
          key: "5",
          grammar: "auth='Digest realm=\"rutorrent\"'",
          usage: "搜索http响应头auth字段相应资产",
        },
        {
          key: "6",
          grammar: 'location="/login.action"',
          usage: '搜索http响应头location字段含"/login.action"资产',
        },
        {
          key: "7",
          grammar: 'icp="京ICP证030173号"',
          usage: '搜索icp备案号含"京ICP证030173号"的资产',
        },
        {
          key: "8",
          grammar: 'copyright="绿盟科技"',
          usage: "搜索版权信息含“绿盟科技”的资产",
        },
        {
          key: "9",
          grammar: 'body_size="102"',
          usage: "搜索指定body长度的资产",
        },
        {
          key: "10",
          grammar: 'url=".gov.cn"',
          usage: "搜索url含.gov.cn的资产",
        },
        {
          key: "11",
          grammar: 'status_code="200"',
          usage: "搜索http响应码为200的资产",
        },
        {
          key: "12",
          grammar: 'server=="Microsoft-IIS/10"',
          usage: "搜索响应头server字段为Microsoft-IIS/10的资产",
        },
      ],
      infoData: [
        {
          key: "1",
          grammar: 'type="printer"',
          usage: "搜索资产类型为printer的资产",
        },
        {
          key: "2",
          grammar: 'vendor="SHAOLIN"',
          usage: "搜索应用厂商名为SHAOLIN的资产",
        },
        {
          key: "3",
          grammar: 'product="会博通综合知识管理系统"',
          usage: "搜索应用产品名为会博通综合知识管理系统的资产",
        },
        {
          key: "4",
          grammar: 'info_version="1.3.5"',
          usage: "搜索应用版本为1.3.5的资产",
        },
        {
          key: "5",
          grammar: 'family="Microsoft"',
          usage: "搜索家族为Microsoft的资产",
        },
        {
          key: "6",
          grammar: 'domain="google.com"',
          usage: "搜索主机名为google.com的资产",
        },
      ],
      CAData: [
        {
          key: "1",
          grammar: 'cert_is_expired="true"',
          usage: "搜索证书已过期的资产",
        },
        {
          key: "2",
          grammar: 'cert="baidu"',
          usage: "搜索原文中有baidu文本的资产",
        },
        {
          key: "3",
          grammar: 'cert_subject="Oracle Corporation"',
          usage: "搜索证书持有者为Oracle Corporation的资产",
        },
        {
          key: "4",
          grammar: 'cert_issuer="DigiCert"',
          usage: "搜索证书颁发者为DigiCert的资产",
        },
        {
          key: "5",
          grammar: 'cert_chain_count="3"',
          usage: "搜索证书链长度为3的资产",
        },
        {
          key: "6",
          grammar: 'cert_pubkey.bits="256"',
          usage: "搜索服务器公钥的密钥长度为256的资产",
        },
        {
          key: "7",
          grammar: 'cert_pubkey.type="RSA"',
          usage: "搜索服务器公钥类型为RSA的资产",
        },
        {
          key: "8",
          grammar: 'cert_serial_number="18460192207935675900910674501"',
          usage: "搜索与证书序列号对应的资产",
        },
        {
          key: "9",
          grammar: 'cert_is_trust="true"',
          usage: "验证证书是否可信",
        },
      ],
    };
  },
  methods: {
    queryClose() {
      this.$emit("closeQueryModal");
    },
    // 跳转到搜索页面
    goToSearch(value) {
      // 关闭弹窗
      this.queryClose();
      this.$router.push({
        path: "/search",
        query: {
          query: value,
          time: new Date().getTime(), // 当前时间戳
          searchWay: "syntax", // 当前搜索方式
        },
      });
    },
    //模拟锚点跳转
    goAnchor(selector) {
      document.querySelector(selector).scrollIntoView({
        behavior: "smooth",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.query {
  height: 550px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
}

.classification {
  display: block;
  margin: 15px 0;
  text-align: center;
  color: #000;
}
</style>
