<template>
	<a-modal
		:visible="visible"
		:maskClosable="false"
		@cancel="cancel"
		title="暴露面查询下载"
		width="700px"
		:footer="null"
	>
		<div>
			<a-form-model
				ref="downloadExposedForm"
				v-bind="formItemLayout"
				labelAlign="left"
			>
				<a-form-model-item label="检索条件" prop="query">
					<multiselect
						v-model="form.keywords"
						placeholder="请输入要检索的公司/ASN归属名称，等待结果出来后选择即可"
						selectLabel="点击或者回车选择"
						selectedLabel="已选"
						deselectLabel="点击或者回车取消选择"
						open-direction="bottom"
						:options="querySource"
						:multiple="true"
						:searchable="true"
						:loading="queryLoading"
						:internal-search="false"
						:clear-on-select="false"
						:close-on-select="false"
						:options-limit="300"
						:max-height="600"
						:show-no-results="false"
						:allow-empty="false"
						:preserve-search="true"
						@search-change="asyncQueryFind"
					>
						<span slot="noOptions"> 暂无选项，请输入关键词检索 </span>
						<template slot="clear" slot-scope="props">
							<div
								class="multiselect__clear"
								v-if="form.keywords.length"
								@mousedown.prevent.stop="clearQuery(props.search)"
							>
								<a-icon type="close" />
							</div>
						</template>
						<span slot="noResult">匹配不到结果，请换个关键词检索</span>
					</multiselect>
				</a-form-model-item>
				<a-form-model-item label="导出字段" prop="search_fields">
					<multiselect
						v-model="form.search_fields"
						:options="fieldOption"
						:multiple="true"
						placeholder="请选择要导出的字段"
						:allow-empty="false"
						:clear-on-select="false"
						:close-on-select="false"
						label="title"
						track-by="value"
						selectLabel="点击或者回车选择"
						selectedLabel="已选"
						deselectLabel="点击或者回车取消选择"
						selectGroupLabel="点击或者回车选择分组"
						deselectGroupLabel="点击或者回车取消选择分组"
						group-values="fields"
						group-label="group"
						:group-select="true"
					>
						<!-- <template slot="clear" slot-scope="props">
							<div
								class="multiselect__clear"
								v-if="form.keywords.length"
								@mousedown.prevent.stop="clearFields(props.search)"
							>
								<a-icon type="close" />
							</div>
						</template> -->
					</multiselect>
				</a-form-model-item>
				<a-form-model-item v-bind="tailFormItemLayout">
					<a-button
						type="primary"
						@click="handleSubmit"
						:loading="downloadLoading"
						>提交并导出</a-button
					>
				</a-form-model-item>
			</a-form-model>
		</div>
	</a-modal>
</template>

<script>
import Multiselect from "vue-multiselect";
import { exposeData, exposeTips } from "@/api/baiduMap";
import { debounce } from "@/util/tool";
const OP = [
	{
		title: "IP",
		value: "ip",
	},
	{
		title: "端口",
		value: "port",
	},
	{
		title: "发现时间",
		value: "last_seen",
	},
	{
		title: "国家",
		value: "country",
	},
	// {
	// 	title: "国家代码",
	// 	value: "country_code",
	// },
	{
		title: "省份",
		value: "region",
	},
	{
		title: "城市",
		value: "city",
	},
	{
		title: "区县",
		value: "area",
	},
	//
	{
		title: "运营商",
		value: "isp",
	},
	{
		title: "应用场景",
		value: "usage",
	},
	{
		title: "拥有者",
		value: "owner",
	},
	{
		title: "ASN归属",
		value: "as_name",
	},
	//
	{
		title: "ASN号",
		value: "asn",
	},
	{
		title: "经度",
		value: "longitude",
	},
	{
		title: "纬度",
		value: "latitude",
	},
	// {
	// 	title: "服务",
	// 	value: "service",
	// },
	//
	{
		title: "站点标题",
		value: "title",
	},
	{
		title: "类型",
		value: "type",
	},
	{
		title: "厂商",
		value: "vendor",
	},
	{
		title: "产品",
		value: "product",
	},
	{
		title: "版本",
		value: "version",
	},
	{
		title: "站点icon哈希",
		value: "icon_hash",
	},
	// "ip",
	// "port",
	// "last_seen",
	// "continent",
	// "country",
	// "country_code",
	// "region",
	// "city",
	// "area",
	// "isp",
	// "usage",
	// "owner",
	// "as_name",
	// "asn",
	// "longitude",
	// "service",
	// "latitude",
	// "title",
	// "type",
	// "vendor",
	// "product",
	// "version",
	// "icon_hash"
];

export default {
	name: "downloadExposedModal",
	components: {
		Multiselect,
	},
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		// q: {
		// 	type: String,
		// 	default: "",
		// },
	},
	data() {
		return {
			downloadLoading: false,
			formItemLayout: {
				labelCol: {
					xs: { span: 24 },
					sm: { span: 6 },
				},
				wrapperCol: {
					xs: { span: 24 },
					sm: { span: 18 },
				},
			},
			tailFormItemLayout: {
				wrapperCol: {
					xs: {
						span: 24,
						offset: 0,
					},
					sm: {
						span: 18,
						offset: 6,
					},
				},
			},
			form: {
				keywords: [], //this.q,
				search_fields: [
					{
						title: "IP",
						value: "ip",
					},
					{
						title: "端口",
						value: "port",
					},
					{
						title: "发现时间",
						value: "last_seen",
					},
				],
			},
			queryLoading: false,
			// selectedQuery: [],
			querySource: [],
			// defaultSearchFields: ["ip", "port", "last_seen"],
			fieldOption: [
				{
					group: "全部字段",
					fields: OP,
				},
			],
		};
	},
	computed: {
		reqData() {
			let result = {
				keywords: this.form.keywords,
				search_fields: this.form.search_fields.map((v) => v.value),
			};
			return result;
		},
	},
	mounted() {},
	methods: {
		cancel() {
			this.$emit("close");
		},
		handleSubmit(e) {
			e.preventDefault();
			if (!this.reqData.keywords.length || !this.reqData.search_fields.length) {
				this.$message.info("请先填写表单信息");
				return;
			}
			console.log("下载进行中……");
			this.download(this.reqData);
		},
		download(data) {
			this.downloadLoading = true;
			exposeData(data)
				.then((resp) => {
					let hearderType = "application/vnd.ms-excel;charset=utf-8";
					//"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
					let blob = new Blob([resp.data], { type: hearderType });
					let url = window.URL.createObjectURL(blob);
					let link = document.createElement("a");
					link.target = "_blank";
					link.href = url;
					link.download = `暴露面查询结果-${data.keywords.join(",")}.xls`; //`NTI - 威胁研判报告 - ${value}.csv`;
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);

					this.downloadLoading = false;
					this.$notification.success({
						message: "下载成功！",
						description: "你已经成功下载暴露面资产信息，请注意查看浏览器左下角",
					});
				})
				.catch((err) => {
					console.log("err---", err);
					this.$message.error(err.message || "响应失败，请稍后重试");
					this.downloadLoading = false;
				});
		},
		asyncQueryFind: debounce(function (query) {
			if (!query) return;
			this.queryLoading = true;
			exposeTips({ keyword: query })
				.then((resp) => {
					this.querySource = [
						...this.form.keywords,
						...window._get(resp, "data.data", []),
					];
				})
				.catch((err) => {
					console.log("exposeTips err", err);
				})
				.finally(() => {
					this.queryLoading = false;
				});
		}, 400),
		clearQuery() {
			this.form.keywords = [];
		},
		// clearFields(){
		// 	this.form.keywords = [];
		// }
	},
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style  lang="scss">
.multiselect__clear {
	position: absolute;
	right: 41px;
	display: block;
	cursor: pointer;
	z-index: 2;
}
</style>