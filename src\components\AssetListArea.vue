<template>
  <!-- 显示内容加载中 -->
  <div>
    <div v-show="listIsLoading" class="loadingMove">
      <a-skeleton active />
      <a-skeleton active />
      <a-skeleton active />
    </div>
    <!--        搜索内容列表渲染-->
    <a-table
      v-show="!listIsLoading"
      style="width: 100%"
      :columns="columns"
      :data-source="assetListData"
      :pagination="pagination"
      :scroll="{ x: table_width }"
     
    >
      <!-- <span slot="asset_industry" slot-scope="record">
				<span v-show="record.length === 0">-</span>
				<a-popover v-show="record.length > 0" placement="top">
					<template slot="content">
						<div style="max-height: 120px; overflow-y: auto;">
							<div
								v-for="(item, index) in record"
								:key="index"
								style="height: 24px;"
							>
								{{ item }}
							</div>
						</div>
					</template>
					<a-tag color="blue" style="cursor: pointer;">
						{{ record[0] }}
					</a-tag>
					<span style="cursor: pointer;">共{{ record.length }}条</span>
				</a-popover>
			</span> -->
      <span slot="usage" slot-scope="text">
        <a-tooltip placement="topLeft">
          <span style="cursor: pointer" @click="addTag('usage', text)">{{
            text
          }}</span>
          <template slot="title">
            {{ text }}
          </template>
        </a-tooltip>
      </span>
      <span slot="asset_tags" slot-scope="record">
        <span v-show="record.length === 0">-</span>
        <a-popover v-show="record.length > 0" placement="top">
          <template slot="content">
            <div style="overflow-x: auto">
              <a-tag
                v-for="(item, index) in record"
                :key="index"
                color="cyan"
                style="cursor: pointer"
                @click="addTag('asset_tags', item)"
              >
                {{ item }}
              </a-tag>
            </div>
          </template>
          <a-tag
            color="cyan"
            style="cursor: pointer"
            @click="addTag('asset_tags', record[0])"
          >
            {{ record[0] }}
          </a-tag>
          <span style="cursor: pointer">共{{ record.length }}条</span>
        </a-popover>
      </span>
      <span
        slot="IP"
        slot-scope="text, record"
        @mouseover="record.showIpCopyIcon = true"
        @mouseout="record.showIpCopyIcon = false"
      >
        <a-popover placement="top" overlayClassName="toolTip">
          <template slot="content">
            查看 <span style="color: #0a88ff">{{ text }}</span> IP详情
          </template>
          <a-icon
            style="cursor: pointer"
            type="copy"
            v-show="record.showIpCopyIcon"
            v-clipboard:copy="text"
            v-clipboard:success="onCopySuccess"
            v-clipboard:err="onCopyError"
          />
          <a @click="IPDetail(record)">
            {{ text }}
          </a>
        </a-popover>
      </span>
      <span slot="owner" slot-scope="text">
        <a-tooltip placement="topLeft">
          <span style="cursor: pointer" @click="addTag('owner', text)">{{
            text
          }}</span>
          <template slot="title">
            {{ text }}
          </template>
        </a-tooltip>
      </span>
      <span slot="isp" slot-scope="text">
        <a-tooltip placement="topLeft">
          <span style="cursor: pointer" @click="addTag('isp', text)">{{
            text
          }}</span>
          <template slot="title">
            {{ text }}
          </template>
        </a-tooltip>
      </span>
      <span slot="ports" slot-scope="text">
        <span v-if="text === '-'">-</span>
        <a-tag
          v-else
          color="blue"
          style="cursor: pointer"
          @click="addTag('port', text)"
        >
          {{ text }}
        </a-tag>
      </span>
      <span slot="protocol" slot-scope="text, record">
        <span v-if="text === '-' && record.transport === '-'">-</span>
        <a-tag
          v-if="text !== '-'"
          color="orange"
          style="cursor: pointer"
          @click="addTag('protocol', text)"
        >
          {{ text }}
        </a-tag>
        <a-tag
          v-if="record.transport !== '-'"
          :color="record.transport === 'TCP' ? 'green' : 'cyan'"
          style="cursor: pointer"
          @click="addTag('transport_protocol', record.transport)"
        >
          {{ record.transport }}
        </a-tag>
      </span>
      <span slot="station" slot-scope="text">
        <a-tooltip placement="topLeft">
          <span style="cursor: pointer">{{ text }}</span>
          <template slot="title">
            {{ text }}
          </template>
        </a-tooltip>
      </span>
      <span slot="enterprise" slot-scope="text">
        <a-tooltip placement="topLeft">
          <span style="cursor: pointer">{{ text }}</span>
          <template slot="title">
            {{ text }}
          </template>
        </a-tooltip>
      </span>
      <span slot="ICP" slot-scope="record">
        <span v-if="!record[0]">-</span>
        <a-tooltip v-else placement="topLeft" overlayClassName="toolTip">
          <template slot="title">
            <div v-for="(item, index) in record" :key="index">
              <span>{{ item }} </span>
            </div>
          </template>
          <span style="cursor: pointer">{{ record[0] }}</span>
        </a-tooltip>
      </span>
      <span slot="status" slot-scope="text">
        <span v-if="text === '-'">-</span>
        <a-tag
          v-else
          :color="
            Number(text) < 300 && Number(text) >= 200 ? '#87d068' : '#f50'
          "
          style="cursor: pointer"
          @click="addTag('status_code', text)"
        >
          {{ text }}
        </a-tag>
      </span>
      <!-- 地理位置 -->
      <span slot="location" slot-scope="text">
        <a-tooltip placement="topLeft">
          <!--    国旗图标-->
          <span
            :class="
              'fi fi-' +
              (text.hasOwnProperty('country_code') && text.country_code
                ? text.country_code.toLowerCase()
                : '')
            "
          ></span>
          <span style="cursor: pointer; margin-left: 4px"
            >{{ text.region }}&nbsp;{{ text.city }}&nbsp;{{ text.area }}
          </span>
          <template slot="title">
            {{ text.region }}&nbsp;{{ text.city }}&nbsp;{{ text.area }}
          </template>
        </a-tooltip>
      </span>
      <!-- 域名 -->
      <span slot="domain" slot-scope="record">
        <span v-if="record.domain === '-'">-</span>
        <span
          v-else
          @mouseover="record.showDomainCopyIcon = true"
          @mouseout="record.showDomainCopyIcon = false"
        >
          <a-icon
            type="copy"
            v-show="record.showDomainCopyIcon"
            style="cursor: pointer"
            v-clipboard:copy="record.domain"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
          />
          <a-popover placement="top">
            <span
              @click="domainClick(record.url)"
              style="cursor: pointer; color: #0072ee"
              >{{ record.domain }}</span
            >
            <template slot="content">
              跳转到
              <span
                style="color: #0a88ff; cursor: pointer"
                @click="domainClick(record.url)"
                >{{ record.url }}</span
              >
            </template>
          </a-popover>
        </span>
      </span>
      <!-- 应用/组件 -->
      <span slot="applications" slot-scope="record">
        <span v-if="record.length === 0"> - </span>
        <a-popover v-else placement="topLeft">
          <template slot="content">
            <div style="max-height: 180px; overflow-y: auto">
              <div
                v-for="(item, index) in record"
                :key="index"
                style="height: 30px"
              >
                <span style="margin-right: 8px">{{ item.rule }}</span>
                <a-tag
                  color="#A5D4AD"
                  v-for="(i, index) in item.tag"
                  :key="index"
                  >{{ i }}</a-tag
                >
              </div>
            </div>
          </template>
          <a-tag
            style="
              cursor: pointer;
              max-width: 100%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            "
          >
            {{ record[0].rule }}
          </a-tag>
          <span style="cursor: pointer">共{{ record.length }}条</span>
        </a-popover>
      </span>
      <!-- 图标哈希 -->
      <span slot="icon_hash" slot-scope="record">
        <span v-show="record === null">-</span>
        <a-popover
          v-if="record !== null && record.mmh3.length > 0"
          placement="top"
        >
          <template slot="content">
            <div style="max-height: 180px; overflow-y: auto">
              <div
                v-if="record.mmh3.length > 0"
                style="white-space: nowrap; height: 30px"
              >
                <span style="margin-right: 8px">mmh3</span>
                <a-tooltip
                  placement="top"
                  v-for="(i, index) in record.mmh3"
                  :key="index"
                >
                  <template slot="title">
                    <span style="white-space: nowrap">
                      {{ i }}
                    </span>
                  </template>
                  <a-tag @click="addTag('icon_hash', i)">
                    {{ i }}
                  </a-tag>
                </a-tooltip>
              </div>
            </div>
          </template>
          <span v-if="record.mmh3.length > 0">
            {{ record.mmh3[0] }}
          </span>
          <span style="cursor: pointer">
            &nbsp;&nbsp; 共{{ record.mmh3.length }}条</span
          >
        </a-popover>
      </span>
    </a-table>
  </div>
</template>
<script>
import { getAssetList } from "@/api/search";
import { pagination_data } from "@/util/sessionUtils";
import { onCopySuccess, onCopyError } from "@/util/tool";
export default {
  name: "AssetListArea",
  props: ["searchValue", "startTime", "endTime"],
  emits: ["addtag"],
  data() {
    return {
      usingTime: 0, // 搜索使用时间
      listIsLoading: true, // 资产列表是否正在加载中
      table_width: 2000,
      full_columns: [
        // {
        // 	title: "资产行业",
        // 	dataIndex: "asset_industry",
        // 	width: "120px",
        // 	ellipsis: true,
        // 	scopedSlots: {customRender: "asset_industry"},
        // 	fixed: "left",
        // },
        {
          title: "应用场景",
          dataIndex: "usage",
          width: "100px",
          ellipsis: true,
          scopedSlots: { customRender: "usage" },
          fixed: "left",
        },
        {
          title: "资产标签",
          dataIndex: "asset_tags",
          width: "160px",
          ellipsis: true,
          scopedSlots: { customRender: "asset_tags" },
          fixed: "left",
        },
        {
          title: "IP",
          dataIndex: "IP",
          width: "120px",
          fixed: "left",
          scopedSlots: { customRender: "IP" },
        },
        {
          title: "ASN归属",
          dataIndex: "owner",
          width: "120px",
          ellipsis: true,
          fixed: "left",
          scopedSlots: { customRender: "owner" },
        },
        {
          title: "ISP运营商",
          dataIndex: "isp",
          width: "120px",
          ellipsis: true,
          scopedSlots: { customRender: "isp" },
        },
        {
          title: "端口",
          dataIndex: "ports",
          width: "95px",
          scopedSlots: { customRender: "ports" },
        },
        {
          title: "协议",
          dataIndex: "protocol",
          width: "150px",
          scopedSlots: { customRender: "protocol" },
        },
        {
          title: "域名",
          dataIndex: "domain",
          width: "160px",
          // width: "400px",
          ellipsis: true,
          scopedSlots: { customRender: "domain" },
        },
        {
          title: "应用/组件",
          dataIndex: "applications",
          width: "200px",
          ellipsis: true,
          scopedSlots: { customRender: "applications" },
        },
        {
          title: "站点标题",
          dataIndex: "station",
          width: "180px",
          ellipsis: true,
          scopedSlots: { customRender: "station" },
        },
        {
          title: "ICP备案企业",
          dataIndex: "enterprise",
          width: "150px",
          ellipsis: true,
          scopedSlots: { customRender: "enterprise" },
        },
        {
          title: "ICP网站备案号",
          dataIndex: "ICP",
          width: "180px",
          ellipsis: true,
          scopedSlots: { customRender: "ICP" },
        },
        {
          title: "状态码",
          dataIndex: "status",
          width: "90px",
          scopedSlots: { customRender: "status" },
        },
        {
          title: "地理位置",
          dataIndex: "location",
          width: "190px",
          ellipsis: true,
          scopedSlots: { customRender: "location" },
        },
        {
          title: "banner哈希",
          dataIndex: "banner_hash",
          width: "120px",
          ellipsis: true,
          scopedSlots: { customRender: "banner_hash" },
        },
        {
          title: "图标哈希",
          dataIndex: "icon_hash",
          width: "180px",
          ellipsis: true,
          scopedSlots: { customRender: "icon_hash" },
        },
        {
          title: "更新时间",
          width: "100px",
          dataIndex: "last",
          fixed: "right",
        },
      ],
      columns: this.full_columns,
      showColumns: [
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
      ],
      pageSize: 10,
      currentPage: 1,
      pagination: {
        current: 1,
        total: 0,
        pageSize: 10,
        // defaultPageSize: 10,
        showTotal: () => {
          return (
            <div>
              共{" "}
              <span style="color:#0072ee">
                {this.statisticsTotal.toLocaleString()}
              </span>{" "}
              条数据，用时 <span style="color:#0072ee">{this.usingTime}</span>{" "}
              秒
            </div>
          );
        },
        showQuickJumper: true,
        showSizeChanger: true, // 可以改变每页个数
        pageSizeOptions: ["10", "20", "50", "100", "150", "200"],
        onShowSizeChange: (current, pageSize) =>
          this.changePageSize(current, pageSize),
        onChange: (current) => this.changePage(current),
      },


      assetListData: [], // 资产列表数据
      assetListResponseData: [], // 资产列表端口响应数据
      statisticsTotal: 0, // 资产总数
    };
  },
  methods: {
    onCopyError,
    onCopySuccess,
    determineCode(code) {
      if (code == 10002) {
        // 正常不会出现
        this.$message.info(`每页数据量超限, 最大值200`, 3);
      } else if (code == 10003) {
        this.$message.info(`数量超限, 最多可查看10000条数据`, 3);
      } else if (code == 10004) {
        this.$message.info(`输入值格式有误, 请排查语句标点符号等`, 3);
      } else if (code == 0) {
        return true;
      }
      return false;
    },



    // 获取资产列表数据
    getAssetListData(
      q,
      start = this.startTime,
      end = this.endTime,
      page = 1,
      page_size = 10
    ) {
      this.listIsLoading = true;
      let startTime = new Date().getTime();
      this.assetListData = [];
      this.showColumns = Array(17).fill(false);

      getAssetList({
        query: btoa(unescape(encodeURIComponent(q))),
        start_time: start,
        end_time: end,
        page: page,
        page_size: page_size,
      })
        .then((res) => {
          if (this.determineCode(res.data.code)) {
            this.statisticsTotal = res.data.total.toLocaleString();
            this.pagination.total = res.data.total;
            // 搜索结果为空
            if (res.data.total === 0) {
              this.$message.info(
                `请尝试扩大筛选时间区间或者修改搜索关键词哦~`,
                3
              );
            }
            // 搜索结果不为空
            else {
              this.assetListResponseData = res.data.data;
              this.assetListResponseData.forEach((item, index) => {
                let obj = {};
                let httpResponse = item.http_response;
                obj.key = index + 1;
                obj.number = index + 1;
                let tagList = [];
                item.info.forEach((i) => {
                  tagList = tagList.concat(i.tag);
                });
                if (tagList.length === 0) obj["tag"] = "-";
                else {
                  obj["tag"] = tagList;
                }
                // obj.asset_industry =
                //   this._get(item, "asset_industry", "-") || "-";
                if (item.location.usage !== "") {
                  this.showColumns[0] = true;
                }
                obj.usage = this._get(item.location, "usage", "-") || "-";

                if (item.asset_tags && item.asset_tags.length > 0) {
                  this.showColumns[1] = true;
                }
                obj.asset_tags = this._get(item, "asset_tags", "-") || "-";
                if (item.ip !== "") {
                  this.showColumns[2] = true;
                }
                obj.IP = item.ip;
                if (item.location.owner !== "") {
                  this.showColumns[3] = true;
                }
                obj.owner = this._get(item.location, "owner", "-") || "-";
                if (item.location.isp !== "") {
                  this.showColumns[4] = true;
                }
                obj.isp = this._get(item.location, "isp", "-") || "-";
                if (item.port !== "") {
                  this.showColumns[5] = true;
                }
                obj.ports = this._get(item, "port", "-") || "-";
                if (item.protocol !== "" || item.transport !== "") {
                  this.showColumns[6] = true;
                }
                obj.protocol = this._get(item, "protocol", "-") || "-";
                obj.transport =
                  this._get(item, "transport_protocol", "-") || "-";
                if (
                  item.protocol === "HTTP" ||
                  item.protocol === "HTTPS" ||
                  (Object.keys(httpResponse).length > 0 &&
                    httpResponse.domain !== "")
                ) {
                  this.showColumns[7] = true;
                }
                const regex = /^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$/g;
                let domain = "-";
                if (
                  Object.keys(httpResponse).length > 0 &&
                  httpResponse.domain &&
                  httpResponse.domain !== ""
                ) {
                  domain = this._get(httpResponse, "domain", "-") || "-";
                  domain = domain.replace(regex, "");
                } else if (
                  item.protocol === "HTTP" ||
                  item.protocol === "HTTPS"
                ) {
                  domain = item.ip;
                }
                obj.domain = {
                  domain: domain,
                  port: this._get(item, "port", "-") || "-",
                  protocol: this._get(item, "protocol", "-") || "-",
                };
                let url = this.generateURL(obj.domain);
                obj.domain.url = url;
                obj.domain.showDomainCopyIcon = false;
                if (item.info && item.info.length > 0) {
                  this.showColumns[8] = true;
                }
                obj.applications = item.info;
                if (
                  Object.keys(httpResponse).length > 0 &&
                  httpResponse.title !== ""
                ) {
                  this.showColumns[9] = true;
                }
                obj.station = this._get(httpResponse, "title", "-") || "-";
                if (httpResponse.enterprise && httpResponse.enterprise !== "") {
                  this.showColumns[10] = true;
                }
                obj.enterprise =
                  this._get(httpResponse, "enterprise", "-") || "-";
                if (
                  Object.keys(httpResponse).length > 0 &&
                  httpResponse.icp &&
                  httpResponse.icp.length > 0
                ) {
                  this.showColumns[11] = true;
                }
                obj.ICP = this._get(httpResponse, "icp", "-") || "-";
                if (
                  Object.keys(httpResponse).length > 0 &&
                  httpResponse.status_code !== ""
                ) {
                  this.showColumns[12] = true;
                }
                obj.status = this._get(httpResponse, "status_code", "-") || "-";
                if (item.location.country_code !== "") {
                  this.showColumns[13] = true;
                }
                obj.location = item.location || {}; //item.location.country_code + item.location.region + item.location.city + item.location.area;
                if (item.banner_hash !== "") {
                  this.showColumns[14] = true;
                }
                obj.banner_hash = this._get(item, "banner_hash", "-") || "-";
                if (
                  item.icon_hash &&
                  Object.keys(item.icon_hash).length > 0 &&
                  item.icon_hash.mmh3.length > 0
                ) {
                  this.showColumns[15] = true;
                }
                obj.icon_hash =
                  item.icon_hash && Object.keys(item.icon_hash).length > 0
                    ? item.icon_hash
                    : null;
                obj.last =
                  (item.last_seen && item.last_seen.slice(0, 10)) || "-";
                obj.showIpCopyIcon = false;
                // obj.showDomainCopyIcon = false;

                this.assetListData.push(obj);
                let endTime = new Date().getTime();
                this.usingTime = (endTime - startTime) / 1000;
              });
              this.showColumns[16] = true;
              let new_columns = [];
              let table_width = 0;
              this.showColumns.forEach((b, idx) => {
                if (b) {
                  new_columns.push(this.full_columns[idx]);
                  table_width += Number(
                    this.full_columns[idx].width.slice(0, -2)
                  );
                }
              });
              this.table_width = table_width;
              this.columns = [...new_columns];
            }
          } else {
            this.statisticsTotal = 0;
            this.pagination.total = 0;
            this.assetListResponseData = null;
          }
          this.listIsLoading = false;
        })
        .catch((err) => {
          console.log("getAssetList err", err);
          this.assetListData = [];
          this.pagination.total = 0;
          this.listIsLoading = false;
          let endTime = new Date().getTime();
          this.usingTime = (endTime - startTime) / 1000;
        });
    },
    // 更改分页大小
    changePageSize(current, size) {
      this.pagination.current = Number(current);
      this.pagination.pageSize = Number(size);
      this.pageSize = size;
      this.currentPage = current;
      // 存缓存，以方便刷新后恢复
      pagination_data.savePagination(`${current} ${size}`);
      this.getAssetListData(
        this.searchValue,
        this.startTime,
        this.endTime,
        current,
        size
      );
    },
    // 点击换页
    changePage(page) {
      this.pagination.current = Number(page);
      // 存缓存，以方便刷新后恢复
      pagination_data.savePagination(`${page} ${this.pageSize}`);
      this.getAssetListData(
        this.searchValue,
        this.startTime,
        this.endTime,
        page,
        this.pageSize
      );
    },
    // 跳转到IP详情页面
    IPDetail(record) {
      // this.$router.push({
      //   path: "/details",
      //   query: {
      //     query: this.searchValue,
      //     ip: record.IP,
      //     domain: record.domain.domain,
      //     port: record.ports,
      //     transport: record.transport,
      //     searchWay: this.searchWay,
      //   },
      // });
      const queryParams = {
        query: this.searchValue,
        ip: record.IP,
        domain: record.domain.domain,
        port: record.ports,
        transport: record.transport,
        searchWay: this.searchWay,
      };

      const query = Object.keys(queryParams)
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`
        )
        .join("&");

      window.open(`/details?${query}`);
    },
    addTag(tag, text) {
      this.$emit("addtag", tag, text);
    },
    setPagination(key, value) {
      this.pagination[key] = value;
    },
    generateURL(value) {
      if (value.domain === "-") {
        return "";
      }
      let port = `:${value.port}`;
      let protocol = value.protocol.toLowerCase();

      if (
        (protocol === "http" && value.port === 80) ||
        (protocol === "https" && value.port === 443)
      ) {
        port = "";
      }
      return `${protocol}://${value.domain}${port}`;
    },
    // 跳转到domain对应的外部页面
    domainClick(url) {
      window.open(url, "_blank");
    },
  },
  mounted() {
    this.$nextTick(() => {
      let pagination_ = pagination_data.getPagination();
      if (pagination_) {
        let [cur, page] = pagination_.split(" ");
        this.pagination.current = Number(cur);
        this.pagination.pageSize = Number(page);
        this.pageSize = Number(page);
      }
    });
  },
};
</script>
<style lang="scss" scoped>
.copy-icon {
  visibility: hidden;
}
.copy-icon-container:hover .copy-icon {
  visibility: visible;
}
.loadingMove {
  width: 100%;
  margin: 24px auto;
}
</style>
