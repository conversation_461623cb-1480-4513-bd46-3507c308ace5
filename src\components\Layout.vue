<template>
  <a-layout class="layout">
    <a-layout-header>
      <Menu/>
    </a-layout-header>
    <a-layout-content style="max-height: calc(100% - 110px);  margin-top: 60px;">
      <keep-alive include="Search">
        <router-view/>
      </keep-alive>
    </a-layout-content>
    <Footer/>
  </a-layout>
</template>

<script>
import Menu from "@/components/Menu";
import Footer from "@/components/Footer";

export default {
  name: "Layout",
  components: {
    Menu,
    Footer,
  },
  data() {
    return {
      currentRoute: "",
    };
  },
  computed: {
  },

  mounted() {

  },

  methods: {},
};
</script>
<style scoped lang="scss">
.layout {
  width: 100%;
  min-height: 100vh;
}

.logo {
  float: left;
  color: rgba(255, 255, 255, 0.65);
  padding-right: 40px;

  img {
    padding-right: 16px;
  }

  &:hover {
    color: #fff;
  }
}
</style>
