const { defineConfig } = require("@vue/cli-service");
const ImageMinimizerPlugin = require("image-minimizer-webpack-plugin");
const PreloadWebpackPlugin = require("@vue/preload-webpack-plugin");
const CompresstionPlugin = require("compression-webpack-plugin");
// const BundleAnalyzerPlugin =
//   require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: false,
  css: {
    // loaderOptions: {
    //   //给 sass-loader 传递选项
    //   sass: {},
    //   scss: {
    //     additionalData: `@import "~@/assets/css/var.scss";`,
    //   },
    //   less: {
    //     // javascriptEnabled: true,
    //     lessOptions: {
    //       javascriptEnabled: true
    //     }
    //   },
    // },
    loaderOptions: {
      //给 sass-loader 传递选项
      sass: {
        additionalData: `@import "@/assets/css/var.scss";`
      },
      scss: {
        // additionalData: `@import "@/assets/css/var.scss";`
        additionalData: `@use "sass:meta"; @import "~@/assets/css/var.scss";`,
        sassOptions: {
          // 禁用弃用警告
          silenceDeprecations: ['import']
        }
      },
      less: {
        // javascriptEnabled: true,
        lessOptions: {
          javascriptEnabled: true
        }
      },
    },
  },
  devServer: {
    open: process.platform === "darwin",
    host: "127.0.0.1",
    port: 3666, //前端开发启动的端口
    https: false,
    proxy: {
      "/api": {
        // target: "http://*************:80",
        target: "http://*************:81",

        changeOrigin: true,
      },
    },
  },
  pluginOptions: {
    i18n: {
      locale: "en",
      fallbackLocale: "en",
      localeDir: "locales",
      enableInSFC: true,
      enableBridge: false,
    },
  },
  configureWebpack: {
    // output: {
    //   chunkFilename: "[name].js",
    // },
    optimization: {
      splitChunks: {
        chunks: "all",
      },
    },
    plugins: [
      // 压缩图片
      new ImageMinimizerPlugin({
        minimizer: {
          implementation: ImageMinimizerPlugin.imageminMinify,
          options: {
            plugins: [
              ["mozjpeg", { quality: 65 }],
              ["optipng", { optimizationLevel: 5 }],
              [
                "svgo",
                {
                  plugins: [
                    "preset-default",
                    "prefixIds",
                    {
                      name: "sortAttrs",
                      params: {
                        xmlnsOrder: "alphabetical",
                      },
                    },
                  ],
                },
              ],
            ],
          },
        },
      }),
      // 给search页增加prefetch
      new PreloadWebpackPlugin({
        rel: "prefetch",
        fileWhitelist: [/search/],
      }),
      //gzip压缩
      new CompresstionPlugin({
        algorithm: "gzip",
        test: /\.(js|css)$/,
        threshold: 10240,
        minRatio: 0.8,
        // deleteOriginalAssets: true,
      }),
    ],
  },
});
