// 全局前置引入变量和函数等

$primary-color : #55A722;
$success-color : #68B92E;
$warning-color : #F59F00;
$error-color : #F52222;
$info-color : #0A88FF;
$unknown-color: #949ca8;

$primary-5 : #68B92E;
$item-active-bg : rgba(85, 167, 34, .10); //覆盖计算的primary-color值
$item-hover-bg : rgba(85, 167, 34, .10);
$table-row-hover-bg : rgba(85, 167, 34, .10);

$link-color : #0072ee;
$link-hover-color : #0A88FF;
$link-active-color : #0056FF;

$btn-danger-color : #fff;
$btn-danger-bg : #ff524d; // color(~`colorPalette('${error-color}', 5) `) 
$btn-danger-border : #ff524d; // color(~`colorPalette('${error-color}', 5) `)  
$layout-header-background : #26303A; //导航颜色
$table-header-bg : #F5F5F5;
// $card-actions-background   : #F7F9FA;
$modal-mask-bg : rgba(0, 0, 0, 0.45);
$header-bg-color: #26313a;
$font-family : -apple-system,
BlinkMacSystemFont,
'Segoe UI',
'PingFang SC',
'Hiragino Sans GB',
'Microsoft YaHei',
'Helvetica Neue',
Helvetica,
Arial,
sans-serif,
'Segoe UI Symbol';