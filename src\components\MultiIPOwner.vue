<template>
    <div>
      <a-skeleton active v-if="data.loading" />
      <div v-else>
        <h1 class="ownerTitle">
          <strong class="ownerTitleText">归属信息</strong>
        </h1>
        <div style="width: 90%; margin: auto">
          <a-table
            :columns="total_columns"
            :data-source="data.data"
            :pagination="data.data && data.data.length <= 10 ? false : {}"
          >
          </a-table>
        </div>
      </div>
    </div>
  </template>
  <script>
  export default {
    name: "MultiIPOwner",
    props: ["data"],
    data() {
      return {
        total_columns: [
          {
            dataIndex: "ip",
            key: "ip",
            title: "IP/域名",
            width: "40%",
          },
          {
            dataIndex: "owner",
            key: "owner",
            title: "归属",
          },
        ],
      };
    },
  };
  </script>
  <style scoped lang="scss">
  .ownerTitle {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 60px;
    margin-bottom: 40px;
    border-bottom: 3px solid #e0dddd;
  
    .ownerTitleText {
      height: 45px;
      font-size: 24px;
      letter-spacing: 1px;
      padding: 0 16px;
      margin-bottom: -3px;
      border-bottom: 3px solid #55a722;
    }
  }
  .subTitle {
    width: 100%;
    position: relative;
    padding-left: 13px;
    margin: 40px 0 12px 0;
    font-size: 20px;
  }
  
  .subTitle:before {
    content: "";
    background-color: #05981d;
    width: 3px;
    height: 22px;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -10px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
  }
  </style>
  
