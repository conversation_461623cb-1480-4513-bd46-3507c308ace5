<template>
    <div class="UserInputContent">
      <div class="card-container">
        <a-tabs type="card" @change="onChange" :activeKey="activekey">
          <a-tab-pane key="1" tab="IP/域名查询">
            <div class="input-outer-container">
              <div class="input-inner-container">
                <a-input-search
                  placeholder="请输入IP/域名"
                  size="large"
                  :allowClear="true"
                  v-model="singleValue"
                  @search="onDebounceSingleSearch"
                >
                  <a-button slot="enterButton" type="primary"> 查询 </a-button>
                </a-input-search>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="IP/域名批量查询">
            <div class="input-outer-container">
              <div class="input-inner-container">
                <a-textarea
                  placeholder="请输入IP/域名（至少输入2个，最多输入20个）"
                  :allowClear="true"
                  :rows="4"
                  v-model="multiValue"
                />
                <div class="multiSearch">
                  <span>批量输入IP/域名，每行输入一个（最多输入20个）</span>
                  <a-button
                    type="primary"
                    size="default"
                    @click="onDebounceMultiSearch"
                  >
                    查询
                  </a-button>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </template>
  <script>
  import {
    ip_single_owner_status,
    ip_multi_owner_status,
  } from "@/util/sessionUtils";
  import { debounce } from "@/util/tool";
  import { testIp } from "@/util/regx";
  import { message } from "ant-design-vue";
  
  export default {
    name: "IPOwnerUserInput",
    data() {
      return {
        activekey: "1",
        singleValue: "",
        multiValue: "",
      };
    },
    methods: {
      onChange(key) {
        this.activekey = key;
        // this.activekey = '1';
        if (Object.keys(this.$route.query).length !== 0) {
          //在结果页面
          if (key === "1") {
            if (this.singleValue) {
              this.onSingleSearch(this.singleValue);
            } else {
              this.$router.push({
                query: {
                  searchWay: "single",
                },
              });
            }
          }
          if (key === "2") {
            if (this.multiValue) {
              this.onMultiSearch(this.multiValue);
            } else {
              this.$router.push({
                query: {
                  searchWay: "multi",
                },
              });
            }
          }
        }
      },
      onSingleSearch(value) {
        value = value.trim();
        this.singleValue = value;
        if (testIp(value)) {
          ip_single_owner_status.saveIPSingle(value);
          this.$router.push({
            query: {
              searchWay: "single",
              time: new Date().getTime(),
            },
          });
        } else {
          message.error("请输入正确格式的IP/域名！");
        }
      },
      onDebounceSingleSearch: debounce(function () {
        this.onSingleSearch(this.singleValue);
      }, 500),
      onMultiSearch() {
        if (!this.multiValue) {
          message.error("请输入正确格式的IP/域名！");
        } else {
          let { valid, value } = this.multiIPValid();
          if (!valid) {
            message.error("请输入正确格式的IP/域名！");
          } else {
            if (value.length <= 1) {
              message.error("请至少输入2个IP！");
              valid = false;
            }
            if (value.length > 20) {
              message.error("最多只能输入20个IP！");
              valid = false;
            }
            if (valid) {
              ip_multi_owner_status.saveIPMulti(value.join(","));
              this.$router.push({
                query: {
                  searchWay: "multi",
                  time: new Date().getTime(),
                },
              });
            }
          }
        }
      },
      onDebounceMultiSearch: debounce(function () {
        this.onMultiSearch(this.multiValue);
      }, 500),
      multiIPValid() {
        let value = this.multiValue.trim().split("\n"); //拆成数组
        let valid = true; //是否所有ip都合法
        value = value.reduce((pre, cur) => {
          if (valid) {
            //是否目前位置所有ip都合法
            if (cur === "") {
              //当前ip是否为空
              return pre;
            } else {
              if (testIp(cur)) {
                //当前ip是否合法
                if (!pre.includes(cur)) {
                  //是否与之前输入的ip重复
                  pre.push(cur);
                }
                return pre;
              } else {
                valid = false;
                return;
              }
            }
          } else {
            return;
          }
        }, []);
        return {
          valid,
          value,
        };
      },
    },
    mounted() {
      if (Object.keys(this.$route.query).length !== 0) {
        if (this.$route.query.searchWay === "single") {
          this.activekey = "1";
        }
        if (this.$route.query.searchWay === "multi") {
          this.activekey = "2";
        }
      }
      this.singleValue = ip_single_owner_status.getIPSingle() ?? "";
      let ips = ip_multi_owner_status.getIPMulti();
      if (ips) {
        this.multiValue = ips.replace(/,/g, "\n");
      } else {
        this.multiValue = "";
      }
    },
    watch: {
      $route: {
        handler: function (to) {
          if (Object.keys(to.query).length === 0) {
            this.singleValue = "";
            this.multiValue = "";
            this.activekey = "1";
          }
        },
      },
    },
  };
  </script>
  <style lang="scss">
  .UserInputContent {
    width: 80%;
    background-color: #fff;
    .multiSearch {
      margin-top: 8px;
      display: flex;
      width: 100%;
      flex-direction: row;
      justify-content: space-between;
    }
    .input-outer-container {
      width: 100%;
      padding: 16px;
    }
    .input-inner-container {
      width: 70%;
      margin: auto;
    }
  }
  
  .card-container {
    overflow: hidden;
    padding-bottom: 24px;
    width: 100%;
  }
  .ant-tabs-bar {
    padding: 0 0;
  }
  .card-container > .ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
    border-color: transparent;
    border-radius: 0;
    background: transparent;
    font-size: 16px;
  }
  .card-container > .ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active {
    border-top: 2px solid #55a722;
    background: linear-gradient(to bottom, #6acf2b5b, 10%, #fff);
  }
  .ant-input {
    font-size: 16px;
  }
  </style>
  
