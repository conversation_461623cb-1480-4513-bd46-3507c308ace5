<template>
    <div class="IPOwnership">
      <div class="container">
        <img
          v-show="!isResult"
          src="../../src/assets/img/banner.svg"
          alt=""
          style="width: 18%; margin: 60px auto"
        />
        <!-- 用户输入界面 -->
        <IPOwnerUserInput style="margin-bottom: 30px;"/>
        <!-- 结果展示界面 -->
        <!-- <div class="ResultContent" v-show="isResult"></div> -->
        <IPOwnerResult v-if="isResult" />
      </div>
    </div>
  </template>
  <script>
  import IPOwnerUserInput from "@/components/IPOwnerUserInput.vue";
  import IPOwnerResult from "@/components/IPOwnerResult.vue";
  import {
    ip_single_owner_status,
    ip_multi_owner_status,
  } from "@/util/sessionUtils";
  
  export default {
    name: "IPOwner",
    components: {
      IPOwnerUserInput,
      IPOwnerResult,
    },
    data() {
      return {
        isResult: false,
      };
    },
    methods: {},
    destroyed() {
      ip_single_owner_status.removeIPSingle();
      ip_multi_owner_status.removeIPMulti();
    },
    mounted() {
      if (this.$route.query.time) {
        this.isResult = true;
      }
    },
    watch: {
      $route: {
        //这里的路由监听只负责页面的显示，不涉及异步查询数据操作
        handler: function (to) {
          if (to.name === "owner") {
            if (Object.keys(to.query).length === 0) {
              //点了右上角的归属查询按钮
              // this.$router.go(0);
              ip_single_owner_status.removeIPSingle();
              ip_multi_owner_status.removeIPMulti();
              // this.$router.go(0);
              this.isResult = false;
            }
            if (to.query.time) {
              //在搜
              this.isResult = true;
            } else {
              //没在搜
              this.isResult = false;
            }
          }
        },
      },
    },
  };
  </script>
  <style lang="scss">
  .IPOwnership {
    position: absolute;
    width: 100%;
    height: calc(100% - 100px);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 3% 10% 20px 10%;
    background-color: rgb(247, 249, 252);
  }
  .container > div {
    border-radius: 15px;
    box-shadow: 4px 4px 4px rgba(218, 215, 215, 0.5);
    overflow: hidden;
  }
  .container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  </style>
  
