<template>
  <!-- TODO: -->
  <a-modal
      :visible="treeModalVisible"
      :destroyOnClose="true"
      @cancel="treeClose"
      title="路由路径"
      width="85%"
      height="600px"
      :bodyStyle="{width: '100%', height: '100%', position: 'relative','overflow-x': 'auto'}"
      :footer="null"
  >
  <!-- <div :style="{ height: '100%',width: maxLen*120 + 'px', position: 'relative'}"> -->
    <VChart class="chart"  ref="treeEchart" :option="option"  @dblclick="dblclick" :loading="loading"
			:loading-options="{
				color: '#68B92E',
				text: '加载中……',
			}"/>
  <!-- </div> -->
    <!-- <div ref="treeEchart" style="height: 70vh;overflow-x: auto"></div> -->
    
  </a-modal>
</template>

<script>
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { TreeChart } from "echarts/charts";
import {
	TitleComponent,
	TooltipComponent,
	LegendComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { getIPTopology } from "@/api/baiduMap";

use([
	CanvasRenderer,
	TreeChart,
	TitleComponent,
	TooltipComponent,
	LegendComponent,
]);
const SVG_PATH = {
  destination : 'path://M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64z m-16 831.2c-199.2-8-359.2-168.8-367.2-367.2h128C264.8 656.8 368 759.2 496 767.2v128z m0-192c-92.8-8-167.2-82.4-175.2-175.2h129.6c5.6 22.4 23.2 40 45.6 45.6v129.6z m0-252.8c-22.4 5.6-39.2 23.2-45.6 45.6H320.8c8-92.8 82.4-167.2 175.2-175.2v129.6z m0-193.6C367.2 264.8 264.8 368 256.8 496h-128c8-199.2 168.8-359.2 367.2-367.2v128z m32 64c17.6 1.6 34.4 5.6 49.6 11.2 4.8-22.4 17.6-41.6 35.2-55.2-26.4-11.2-55.2-18.4-84.8-20.8V128c92 4 176 40 240 97.6L722.4 272c8.8 5.6 17.6 12.8 24 21.6l44.8-44.8c61.6 64.8 100.8 152 104.8 247.2H768c-1.6-29.6-8.8-58.4-20-84.8-13.6 17.6-32.8 30.4-55.2 35.2 5.6 16 9.6 32 11.2 49.6H574.4c-2.4-8-5.6-14.4-10.4-20.8l47.2-47.2c-8-6.4-15.2-15.2-21.6-24l-50.4 50.4c-4-1.6-7.2-3.2-12-4V320.8z m0 253.6c22.4-5.6 40.8-23.2 46.4-46.4h128.8c-8 92.8-82.4 167.2-175.2 175.2V574.4z m0 320.8v-128C656.8 759.2 759.2 656 767.2 528h128c-8 199.2-168 359.2-367.2 367.2z m143.2-478.4c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z',
  ip: "path://M512 1024A512 512 0 1 1 512 0a512 512 0 0 1 0 1024zM383.268571 729.307429V292.571429H292.571429v436.736h90.697142zM714.166857 431.542857c0 22.674286-5.924571 39.277714-17.92 49.737143-11.922286 10.532571-28.672 15.725714-50.176 15.725714H561.005714V368.420571h85.065143c21.504 0 38.253714 4.827429 50.176 14.555429 11.995429 9.654857 17.92 25.819429 17.92 48.566857zM804.571429 430.957714c0-47.250286-13.019429-82.066286-39.131429-104.594285C739.474286 303.835429 704.438857 292.571429 660.553143 292.571429h-190.171429v436.736h90.697143V572.269714h92.672c48.420571 0 85.577143-10.678857 111.689143-31.963428 26.112-21.357714 39.131429-57.782857 39.131429-109.348572z",
  probe: 'path://M719.36 209.92c-43.52-30.72-102.4-51.2-171.52-51.2v89.6c51.2 0 89.6 12.8 120.32 30.72s51.2 56.32 64 102.4l81.92-25.6c-17.92-61.44-51.2-107.52-94.72-145.92zM527.36 537.6c38.4 17.92 81.92 5.12 107.52-25.6 30.72-43.52 25.6-102.4-17.92-133.12-38.4-38.4-94.72-25.6-128 12.8-25.6 30.72-25.6 76.8-5.12 115.2l-51.2 64-307.2-248.32C40.96 435.2 12.8 583.68 51.2 711.68l40.96 94.72c25.6 43.52 64 89.6 107.52 128 184.32 145.92 448 107.52 588.8-76.8l-307.2-256 46.08-64z m-38.4 222.72c0-5.12 5.12-17.92 12.8-12.8l69.12 43.52c5.12 0 5.12 5.12 5.12 12.8l-38.4 76.8c-5.12 5.12-17.92 5.12-17.92 0l-12.8-43.52-133.12 38.4c-12.8 0-17.92 0-25.6-12.8l-25.6-115.2L156.16 793.6c-5.12 0-12.8 0-17.92-12.8V768c0-5.12 0-12.8 12.8-17.92l179.2-43.52c5.12 0 12.8 0 17.92 12.8l5.12 5.12 25.6 107.52 115.2-30.72-5.12-40.96z M821.76 81.92C744.96 25.6 642.56 0 519.68 0v89.6c102.4 0 184.32 17.92 248.32 64s107.52 115.2 133.12 209.92l81.92-25.6c-20.48-107.52-79.36-197.12-161.28-256z',
  router: 'path://M669.9 226.7l-52.6 36.4C632 284.4 640 309.6 640 336s-8 51.6-22.7 72.9l52.6 36.4c21.5-31 34.1-68.7 34.1-109.3s-12.6-78.3-34.1-109.3z m-315.8 0c-21.5 31-34.1 68.7-34.1 109.3s12.6 78.3 34.1 109.3l52.6-36.4C392 387.6 384 362.4 384 336s8-51.6 22.7-72.9l-52.6-36.4zM762.4 162.6L709.8 199c9.1 13.3 16.8 27.4 23.1 42.4 12.7 29.9 19.1 61.7 19.1 94.6 0 32.8-6.4 64.6-19.1 94.6-6.3 14.9-14 29.1-23.1 42.4l52.6 36.4C796.2 460 816 400.3 816 336s-19.8-124-53.6-173.4z m-452.2 33.7l-52.6-36.4C222.5 209.7 202 270.5 202 336s20.5 126.3 55.5 176.2l52.6-36.4c-9.9-14.1-18.3-29.2-25.1-45.2-12.6-30-19-61.8-19-94.6s6.4-64.7 19.1-94.6c6.7-16 15.1-31.1 25.1-45.1zM881.8 80l-52.6 36.4c14.4 21 26.7 43.5 36.7 67.1 20 47.3 30.2 97.6 30.2 149.5S886 435.1 865.9 482.5c-10.7 25.2-23.9 49-39.5 71.2L879 590c51.1-72.8 81-161.4 81-257 0-93.9-28.9-181-78.2-253zM128 333c0-51.9 10.1-102.2 30.2-149.5 10-23.7 22.3-46.1 36.7-67.1L142.2 80C92.9 152 64 239.1 64 333c0 95.6 30 184.3 81 257l52.6-36.4c-15.6-22.2-28.8-46-39.5-71.2-20-47.3-30.1-97.5-30.1-149.4z M1008 640H576c-17.7 0-32-14.3-32-32V320c0-17.7-14.3-32-32-32-8.8 0-16.8 3.6-22.6 9.4-5.8 5.8-9.4 13.8-9.4 22.6v288c0 17.7-14.3 32-32 32H16c-8.8 0-16 7.2-16 16v256c0 8.8 7.2 16 16 16h992c8.8 0 16-7.2 16-16V656c0-8.8-7.2-16-16-16zM128 816v-64h352v64H128z m432-32c0-17.7 14.3-32 32-32s32 14.3 32 32-14.3 32-32 32-32-14.3-32-32z m112 0c0-26.5 21.5-48 48-48s48 21.5 48 48-21.5 48-48 48-48-21.5-48-48z m176 48c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z'
};
const TYPE_MAP = {
  destination: "目的Ip",
  ip: "IP",
  probe: "探测出口节点",
  router: "前一跳路由"
};
const LOSS_ROUTE = "...";
export default {
  name: "IPTopology",
  components:{
    VChart,
  },
  props: {
    treeModalVisible: {
      type: Boolean,
      default: false
    },
    ip: {
      type: String
    },
  },
  data() {
    return {
      maxLen: 10,
      loading: false,
      option: {
        tooltip: { //提示框组件
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: function (params) {
            return `${ params.data.name == LOSS_ROUTE  ? "丢包或者匿名缺失" : (TYPE_MAP[params.data.type] || params.data.name) }: <br/>${ params.data.name } `;
          }
        },
        series: [
          {
            type: 'tree',
            initialTreeDepth: -1,
            data: [],
            // top: '1%',
            // left: '15%',
            // bottom: '1%',
            // right: '7%',
            roam: "move",
            symbolSize: 20,
            itemStyle:{
              color: "rgb(66,146,157)"
            },
            orient: 'RL',
            label: {
              position: 'bottom',
              verticalAlign: 'middle',
              distance: 20
            
            },
            leaves: {
              label: {
                position: 'bottom',
                verticalAlign: 'middle',
                // align: 'right'
              }
            },
            emphasis: {
              // focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      },
      
    }
  },
  mounted() {
    // 获取IP拓扑
    this.getChartData({ip:this.ip});
  },
  watch:{
    'treeModalVisible':function(){
      this.getChartData({ip:this.ip}); //TODO:
    }
  },
  methods: {
    dblclick(e){
      console.log(e);
    },
    treeClose() {
      this.$emit("closeTreeModal")
    },
    formatData(data){
      let seriesData = {
        name: this.ip,
        type: 'destination',
        symbol: SVG_PATH.destination,
        symbolSize: 50,
        label:{
          position: 'right'
        },
        itemStyle: {
          color: 'rgb(111,186,44)'
        },
        children: []
      }
      seriesData.children = data.map(p=>{
        let item = {
          name: !p.formrouter ? LOSS_ROUTE : p.formrouter,
          type: 'router',
          symbol: SVG_PATH.router,
          children: []
        };
        let endStr = ''
        this.maxLen = Math.max(p.path.length,this.maxLen);
        item.children = p.path;
        item.children.pop();
        p.formrouter&&item.children.pop();
        item.children = item.children.reverse().map((v,k)=>{
          endStr+=']}';
          let colorStyle = v.split('.')[3] < 50 ? '"itemStyle":{ "color": "rgb(218,37,29)" }, "level":"high",' : '"level":"low",';
          let label = `"label":{ "position": "${ k%2 == 0 ? "top" : "bottom" }" },`;
          return `{ "name": "${v}", "type": "ip", ${colorStyle} "symbol": "${SVG_PATH.ip}", ${label}  "children": [`
        });
        item.children.push(`{ "name": "${p.probe}","type": "probe", "itemStyle": { "color": "rgb(231,120,23)" }, "label": { "position": "left" }, "symbol": "${SVG_PATH.probe}", "symbolSize": 35}`);
        item.children = [JSON.parse(item.children.join('') + endStr)];
        
        return item;
      });
      // console.log('---item---seriesData.children -',seriesData);
      this.$set(this.option.series[0].data,0,seriesData);
    },
    getChartData(params){
      this.loading = true;
      getIPTopology(params).then((resp) => {
        //
        this.formatData(resp.data&&resp.data.data)
      }).catch((err) => {
        console.log('getChartData err',err);
      }).finally(()=>{
        this.loading = false;
      });
    }
  },

}
</script>
<style lang="scss" scoped>

.chart{
  height: 70vh;
	min-width: 100%; 
}
</style>

