<template>
  <div id="register">
    <div class="card-container">
      <header class="registerHead">
        {{$t('register.title')}}
      </header>
      <a-form-model ref="ruleForm" :model="ruleForm" :rules="rules" v-bind="layout" class="registerForm">
        <!--        用户名-->
        <a-form-model-item has-feedback :label="$t('login.username')" prop="username">
          <a-input v-model="ruleForm.username" type="text" autocomplete="off" style="padding: 20px"/>
        </a-form-model-item>
        <!--        密码-->
        <a-form-model-item has-feedback :label="$t('login.password')" prop="password">
          <a-input v-model="ruleForm.password" type="password" autocomplete="off" style="padding: 20px" />
        </a-form-model-item>
        <!--        确认密码-->
        <a-form-model-item has-feedback :label="$t('register.check')" prop="checkPassword">
          <a-input v-model="ruleForm.checkPassword" type="password" autocomplete="off" style="padding: 20px" />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 14, offset: 8 }">
          <a-button type="primary" @click="submitForm('ruleForm')">
            {{$t('register.register')}}
          </a-button>
          <a-button style="margin-left: 20px" @click="resetForm('ruleForm')">
            {{$t('register.reset')}}
          </a-button>
        </a-form-model-item>
      </a-form-model>
      <router-link to="/login" class="registered">{{$t('register.registered')}}</router-link>
    </div>
  </div>
</template>

<script>
import {message} from "ant-design-vue";

export default {
  name: "Register",
  data() {
    // 用户名校验函数
    let validateName = (rule, value, callback) => {
      // 用户名为空
      if (value === '') {
        callback(new Error(this.$t('login.usernameEmptyError')));
      } else {
        // 用户名符合命名规则
        if(/^[a-zA-Z]+[0-9]?$/g.test(value)) {
          callback();
        }
        else {
          callback(this.$t('login.usernameFormat'));
        }
      }
    };
    // 密码校验函数
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('login.passwordEmptyError')));
      } else {
        if(/[a-zA-Z0-9]+/g.test(value) && value.length>8) {
          callback();
        }
        else {
          callback(new Error(this.$t('register.passwordFormatError')));
        }
      }
    };
    // 二次密码校验函数
    let validateCheckPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('register.confirmPassword')));
      } else {
        if(value === this.ruleForm.password) {
          callback();
        }
        else {
          callback(new Error(this.$t('register.confirmPassword')));
        }
      }
    };
    return {
      ruleForm: {
        username: '',   // 用户名
        password: '',   // 密码·
        checkPassword: '',    // 第二次输入的密码
      },
      rules: {
        username: [
          {
            validator: validateName,
            trigger: ['blur','change']
          }
        ],
        password: [
          {
            validator: validatePass,
            trigger: ['blur','change']
          }
        ],
        checkPassword: [
          {
            validator: validateCheckPassword,
            trigger: ['blur','change']
          }
        ],
      },
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 16 },
      },
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        // 表单通过验证则可以成功注册
        if (valid) {
          console.log(this.ruleForm.username)
          console.log(this.ruleForm.password)
          console.log(this.ruleForm.checkPassword)
          message.success(
              this.$t('register.success')
          );
          // 跳转到登录页面
          this.$router.push({
            name: 'login'
          })
        } else {
          message.error(
              this.$t('register.failed')
          );
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>

<style scoped lang="scss">
#register {
  height: calc(100% - 100px);
  width: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url("@/assets/img/login_bg.png");
  background-size: cover;
    .card-container {
      width: 25%;
      border-radius: 10px;
      background: white;
      display: flex;
      flex-direction: column;
      .registerHead {
        background-color: $primary-5;
        height: 50px;
        line-height: 50px;
        border-radius: 10px 10px 0 0;
        font-size: 20px;
        color: white;
        text-align: center;
      }
      .registerForm {
        padding: 32px 40px 0 40px;
      }
      .registered {
        margin: 0 auto 32px;
        color: $primary-color;
        text-decoration: underline;
        font-size: 16px;
      }
    }
}
</style>