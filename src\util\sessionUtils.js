const IP_SINGLE_OWNER_KEY = "ip_single_owner";
const IP_MULTI_OWNER_KEY = "ip_multi_owner";
const PAGINATION_KEY = "pagination_data";
const TAGS_KEY = "tags_data";
const DATE_KEY = "date_data";

export const ip_single_owner_status = {
  saveIPSingle(ip) {
    sessionStorage.setItem(IP_SINGLE_OWNER_KEY, ip);
  },
  getIPSingle() {
    return sessionStorage.getItem(IP_SINGLE_OWNER_KEY);
  },
  removeIPSingle() {
    sessionStorage.removeItem(IP_SINGLE_OWNER_KEY);
  },
};

export const ip_multi_owner_status = {
  saveIPMulti(ip) {
    sessionStorage.setItem(IP_MULTI_OWNER_KEY, ip);
  },
  getIPMulti() {
    return sessionStorage.getItem(IP_MULTI_OWNER_KEY);
  },
  removeIPMulti() {
    sessionStorage.removeItem(IP_MULTI_OWNER_KEY);
  },
};

export const pagination_data = {
  savePagination(value) {
    sessionStorage.setItem(PAGINATION_KEY, value);
  },
  getPagination() {
    return sessionStorage.getItem(PAGINATION_KEY);
  },
  removePagination() {
    sessionStorage.removeItem(PAGINATION_KEY);
  },
};

export const tags_data = {
  saveTags(tags) {
    sessionStorage.setItem(TAGS_KEY, tags);
  },
  getTags() {
    return sessionStorage.getItem(TAGS_KEY);
  },
  removeTags() {
    sessionStorage.removeItem(TAGS_KEY);
  },
};

export const date_data = {
  saveDate(date) {
    sessionStorage.setItem(DATE_KEY, date);
  },
  getDate() {
    return sessionStorage.getItem(DATE_KEY);
  },
  removeDate() {
    sessionStorage.removeItem(DATE_KEY);
  },
};
