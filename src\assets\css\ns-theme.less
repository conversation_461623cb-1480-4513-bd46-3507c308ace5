@import '~ant-design-vue/dist/antd.less'; // 引入官方提供的 less 样式入口文件

//变量覆盖
@primary-color             : #55A722;         // 全局主色
@success-color             : #68B92E;         // 成功色
@warning-color             : #F59F00;         // 警告色
@error-color               : #F52222;         // 错误色
@info-color                : #0A88FF;         // 提示色
@primary-1                 : #eef6e8;
@primary-5                 : #68B92E;
@gray-5                    : #D9D9D9;         // 组件描边，通用分割线
@item-active-bg            : rgba(85,167,34,.10);
@item-hover-bg             : rgba(85,167,34,.10);
@table-row-hover-bg        : rgba(85,167,34,.10);
@link-color                : #0072ee;         // 连接色
@link-hover-color          : #0A88FF;
@link-active-color         : #0056FF;
@btn-danger-color          : #fff;        
@btn-danger-bg             : #ff524d;
@btn-danger-border         : #ff524d;
@layout-header-background  : #26303A;
@table-header-bg           : #F5F5F5;
@modal-mask-bg             : rgba(0, 0, 0, 0.45);
@font-family               : -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB','Microsoft YaHei', 'Helvetica Neue',Helvetica, Arial, sans-serif,  'Segoe UI Symbol';

//font 家族
body, pre, code, kbd, samp{
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', 'Helvetica Neue',Helvetica, Arial, sans-serif,  'Segoe UI Symbol';
}

a {
  color: @link-color;
}

// font
// 异常页面提示文字（大号）
.font-1 {
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
}

// 页面标题，异常页面提示文字（小号）
.font-2 {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
}

// Dashboard内容区域标题，表格标题，侧滑框标题，对话框标题
.font-3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

// 统计数值（大号）
.font-number-big {
  font-size: 36px;
  font-weight: 600;
  line-height: 44px;
}

// 统计数值（小号）
.font-number-small {
  font-size: 28px;
  font-weight: 500;
  line-height: 36px;
}

//button
.ant-btn-primary{
  box-shadow: none;
}

.btn-danger() {
  color:#fff;
  background-color: #ff524d;
  border-color: #ff524d;
  box-shadow: none;
    &:hover,&:focus {
       color:#fff;
       background-color: #ff7e75;
       border-color: #ff7e75;
    }
    &:active, &.active {
      color:#fff;
      background-color: #d93636;
      border-color: #d93636;
   }
}

.btn-link {
  &:hover {
    color: #0A88FF;
  }
  &:active, &:focus {
    color: #0056FF;
  }
}

//Dropdown
.ant-dropdown-trigger.ant-dropdown-link {
  color: #0072ee;
}

//menu
.ant-menu-horizontal {
  line-height: 60px;
}

.ant-menu-item, .ant-menu-submenu-title{
  padding: 0;
}

.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected{
  background-color: transparent;
}

.ant-menu-dark.ant-menu-submenu-popup .ant-menu-item-selected > a, .ant-menu-dark.ant-menu-submenu-popup .ant-menu-item-selected > a:hover{
  color: @primary-color;
}

//for vue frame   其他框架去掉这一部分代码
.ant-menu-horizontal.ant-menu-dark {
  .ant-menu-item, .ant-menu-submenu {
    font-size: 16px;
  }

  .ant-menu-submenu-selected, .ant-menu-submenu-selected.ant-menu-submenu-open, .ant-menu-item-selected {
    background: @primary-color;
    color: #fff;
  }
}

.ant-menu-dark {
  .ant-menu.ant-menu-sub {
    .ant-menu-item.ant-menu-item-selected {
      color: @primary-color;
    }
  }
}

//与该框架有关，antd-vue中的导航高度是64px
.ant-layout-header {
  height:60px; 
  line-height: 60px;
  padding:0 48px;
  position: fixed;
  width: 100%;
  z-index: 998;
}

//pagination
.ant-pagination-item-active,
.ant-pagination-item-active:focus,
.ant-pagination-item-active:hover {
  border-color: @primary-color;
}
.ant-pagination-options-quick-jumper input:hover,.ant-pagination-simple .ant-pagination-simple-pager input:hover{
  border-color:@primary-color
}

//steps
.ant-steps-item-icon,.ant-steps-item-wait .ant-steps-item-icon{
  font-size:14px;
}

.ant-steps-item-finish .ant-steps-item-icon {
  font-size:16px;
}

//card
.ant-card-head-title {
  padding:16px;
}

.ant-card-actions > li {
  margin:8px 0;
}

.ant-card.ant-card-body{
  padding: 16px 16px 0;
}

.ant-card.ant-card-bordered .ant-card-body{
  padding:24px 48px 32px;
}
 
//popover
.ant-popover-inner-content {
  padding:16px;
}

//Table
.ant-table {
    line-height: 1.5;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  padding: 16px 8px;
}

.ant-table-thead > tr > th {
  padding:18px 8px;
  border-top:1px solid #e8e8e8;
}

.ant-table-thead>tr:first-child>th:first-child, .ant-table-row td:first-child {
    padding-left: 16px;
}

.ant-table-thead > tr > th{
  line-height: 1.2;
}

//Alert
.ant-alert-with-description {
  padding: 16px 16px 16px 64px;
}

//modal
.ant-modal-confirm .ant-modal-body {
  padding:16px 16px 16px 24px;
}

//Tabs
.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab.ant-tabs-tab-disabled {
  background:#F5F5F5;
}

//drawer
.ant-drawer.ant-drawer-right {
  .ant-drawer-close {
    color: #000;
  }

  .ant-drawer-header {
    background-color: #f5f5f5;
  }

  .ant-drawer-content {
    height: calc(100% - 60px);
  }
}

.ant-drawer-right {
  .ant-drawer-content-wrapper {
    margin-top: 60px;
  }
}

//tree
.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: @item-hover-bg;
}

.ant-input-search-enter-button+.ant-input-group-addon, .ant-input-search-enter-button input+.ant-input-group-addon{
  background: @primary-color;
}

.ant-tabs-bar{
  padding: 0 48px;
}

.ant-tabs-tabpane.ant-tabs-tabpane-active{
  padding: 0 48px 0;
}

.ant-tabs-nav .ant-tabs-tab{
  margin: 0;
}

// 隐藏分页组件最后一页 后台无法查询数量大的页数
.ant-pagination-jump-next + .ant-pagination-item{
  display: none;
}

.ant-collapse-content > .ant-collapse-content-box {
  padding: 4px 16px 4px 40px;
}

// 加载中图标
.ant-spin-dot-item{
  background-color: #F5F5F5;
}
