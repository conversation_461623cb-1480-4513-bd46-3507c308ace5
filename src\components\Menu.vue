<template>
	<div id="nav-block">
		<!--    标题和logo-->
		<a-tooltip>
			<template slot="title">{{ $t("manu.domain_name_tip") }}</template>
			<router-link to="/" class="logo">
				<img src="@/assets/img/star_logo.png" alt="" />
				<span class="logo-title">{{ $t("manu.domain_name") }}</span>
			</router-link>
		</a-tooltip>
		<!--    导航栏索引-->
		<ul class="right-menus">
			<li>
				<a-tooltip>
					<template slot="title">归属查询</template>
					<router-link to="/owner" style="cursor: pointer"
						>归属查询</router-link
					>
				</a-tooltip>
			</li>
			<!-- <li>
				<a-tooltip>
					<template slot="title">作战地图(Beta版)</template>
					<router-link to="/map" style="cursor: pointer"
						>作战地图(Beta版)</router-link
					>
				</a-tooltip>
			</li> -->
		</ul>
	</div>
</template>

<script>
export default {
	name: "<PERSON><PERSON>",
	props: {},
	components: {},
	data() {
		return {
			userName: "",
		};
	},
	computed: {},
	created() {},
	methods: {
		logOut() {
			this.userName = "";
		},
	},
};
</script>

<style scoped lang="scss">
#nav-block {
	display: flex;
	align-items: center;
	height: 60px;
}

.logo {
	display: flex;
	align-items: center;
	height: 100%;
	color: white;
	font-size: 18px;
	text-decoration: none;
	img {
		width: 44px;
	}
	.logo-title {
		margin-left: 20px;
		margin-right: 200px;
	}
}

.right-menus {
	line-height: 64px;
	position: absolute;
	right: 50px;
	top: 0;
	text-align: center;
	display: flex;
	margin: 0;
	align-items: center;
	list-style: none;
	li {
		padding-right: 32px;
		a {
			text-decoration: none;
			font-size: 16px;
			color: rgba(255, 255, 255, 0.65);
			&:hover {
				color: white;
			}
		}
		.register {
			padding: 4px 16px;
			border: 1px solid #fff;
			border-radius: 4px;
		}
	}
}
// .username {
// 	margin: 0 10px;
// }
</style>
