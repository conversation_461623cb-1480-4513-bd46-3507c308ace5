// 免密存储本地
export function setCache(index, value) {
  return localStorage.setItem(index, value);
}

// 免密从本地获取数据
export function getCache(index) {
  let result = localStorage.getItem(index);
  try {
    return result && JSON.parse(result);
  } catch (error) {
    console.log("getCache JSON.parse error: ", error);
    return result;
  }
}

// 移除某个数据
export function removeCache(index) {
  return localStorage.removeItem(index);
}
